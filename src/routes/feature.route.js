import { Router } from 'express'
import { authentication } from '../controller/auth.controller.js'
import { createFeature, getAllFeatures, getFeatureById, updateFeature, deleteFeature } from '../controller/feature.controller.js'
import { validate } from '../middleware/validate.middleware.js'
import { createFeatureSchema, updateFeatureSchema } from '../schemas/feature.schema.js'
import { restrictToRoles } from '../middleware/rbac.middleware.js'
import { ROLES } from '../constants/roles.js'

const featureRouter = Router()

// Apply authentication middleware to all feature routes
featureRouter.use(authentication)

// Routes for /api/v1/feature
featureRouter
    .route('/')
    .post(
        restrictToRoles([ROLES.SUPER_ADMIN, ROLES.MEMBER]), // Both admin and members can create features
        validate(createFeatureSchema),
        createFeature,
    )
    .get(getAllFeatures)

// Routes for /api/v1/feature/:id
featureRouter
    .route('/:id')
    .get(getFeatureById)
    .put(restrictToRoles([ROLES.SUPER_ADMIN, ROLES.MEMBER]), validate(updateFeatureSchema), updateFeature)
    .delete(restrictToRoles([ROLES.SUPER_ADMIN]), deleteFeature)

export default featureRouter
