import { Router } from 'express'
import { authentication } from '../controller/auth.controller.js'
import { restrictToRoles } from '../middleware/rbac.middleware.js'
import { ROLES } from '../constants/roles.js'
import {
    createWorkspace,
    getUserWorkspaces,
    getWorkspaceById,
    updateWorkspace,
    deleteWorkspace,
    addUserToWorkspace,
    removeUserFromWorkspace,
    inviteToWorkspace,
    getInviteDetails,
    acceptInvite,
} from '../controller/workspace.controller.js'
import { validate } from '../middleware/validate.middleware.js'
import { createWorkspaceSchema, inviteSchema, updateWorkspaceSchema } from '../schemas/workspace.schema.js'

const workspaceRouter = Router()

workspaceRouter.route('/join/:token').get(getInviteDetails)
workspaceRouter.use(authentication)

workspaceRouter.route('/join/:token').post(acceptInvite)
workspaceRouter.route('/').post(validate(createWorkspaceSchema), createWorkspace).get(getUserWorkspaces)
workspaceRouter.route('/:id').get(getWorkspaceById).put(validate(updateWorkspaceSchema), updateWorkspace).delete(deleteWorkspace)
workspaceRouter.route('/user').post(addUserToWorkspace)
workspaceRouter.route('/:workspace_id/user/:user_id').delete(removeUserFromWorkspace)
workspaceRouter.route('/:id/invite').post(validate(inviteSchema), inviteToWorkspace)

export default workspaceRouter
