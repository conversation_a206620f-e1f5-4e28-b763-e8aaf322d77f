import { Router } from 'express'
import { authentication } from '../controller/auth.controller.js'
import { restrictToRoles } from '../middleware/rbac.middleware.js'
import { ROLES } from '../constants/roles.js'
import { createTask, getAllTasks, getTaskById, updateTask, deleteTask } from '../controller/task.controller.js'
import { validate } from '../middleware/validate.middleware.js'
import { createTaskSchema, updateTaskSchema } from '../schemas/task.schema.js'

const taskRouter = Router()

// Apply authentication middleware to all task routes
taskRouter.use(authentication)

taskRouter
    .route('/')
    .post(restrictToRoles([ROLES.SUPER_ADMIN, ROLES.MEMBER]), validate(createTaskSchema), createTask)
    .get(getAllTasks)

// Routes for /api/v1/task/:id
taskRouter
    .route('/:id')
    .get(getTaskById)
    .put(restrictToRoles([ROLES.SUPER_ADMIN, ROLES.MEMBER]), validate(updateTaskSchema), updateTask)
    .delete(restrictToRoles([ROLES.SUPER_ADMIN, ROLES.MEMBER]), deleteTask)

export default taskRouter
