import { z } from 'zod'

// Schema for creating a new project
export const createProjectSchema = z.object({
    name: z.string().min(1, 'Project name is required'),
    overview: z.string().optional(),
    workspace_id: z.number().int().positive('Workspace ID must be a positive integer'),
    status_id: z.number().int().positive('Status ID must be a positive integer'),
    stack: z.array(z.string()).min(1, 'At least one stack item is required'),
})

// Schema for updating an existing project
export const updateProjectSchema = z
    .object({
        name: z.string().min(1, 'Project name is required').optional(),
        overview: z.string().optional(),
        workspace_id: z.number().int().positive('Workspace ID must be a positive integer').optional(),
        status_id: z.number().int().positive('Status ID must be a positive integer').optional(),
    })
    .refine((data) => Object.keys(data).length > 0, {
        message: 'At least one field must be provided for update',
    })

// Schema for adding a user to a project
export const addUserToProjectSchema = z.object({
    project_id: z.number().int().positive('Project ID must be a positive integer'),
    user_id: z.number().int().positive('User ID must be a positive integer'),
})
