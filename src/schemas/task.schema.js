import { z } from 'zod'

// Schema for creating a new task
export const createTaskSchema = z.object({
    feat_id: z.number().int().positive('Feature ID must be a positive integer').optional(),
    short_code: z.string().min(1, 'Short code is required').optional(),
    title: z.string().min(1, 'Title is required'),
    description: z.string().optional(),
    time_estimate_hrs: z.number().positive('Time estimate must be positive').optional(),
    start_date: z.string().datetime().optional(),
    due_date: z.string().datetime().optional(),
    status: z.number().int().positive('Status ID must be a positive integer'),
    assignee: z.array(z.number().int().positive('Assignee ID must be a positive integer')).optional(),
    department: z.number().int().positive('Department ID must be a positive integer').optional(),
    bug: z.boolean().optional().default(false),
    parent_task_short_code: z.string().optional(),
    priority_id: z.number().int().positive('Priority ID must be a positive integer').optional(),
})

// Schema for updating an existing task
export const updateTaskSchema = z
    .object({
        feat_id: z.number().int().positive('Feature ID must be a positive integer').optional(),
        short_code: z.string().min(1, 'Short code is required').optional(),
        title: z.string().min(1, 'Title is required').optional(),
        description: z.string().optional(),
        time_estimate_hrs: z.number().positive('Time estimate must be positive').optional(),
        start_date: z.string().datetime().optional(),
        due_date: z.string().datetime().optional(),
        status: z.number().int().positive('Status ID must be a positive integer').optional(),
        assignee: z.array(z.number().int().positive('Assignee ID must be a positive integer')).optional(),
        department: z.number().int().positive('Department ID must be a positive integer').optional(),
        bug: z.boolean().optional(),
        parent_task_short_code: z.string().min('Parent task short code is required').optional(),
        priority_id: z.number().int().positive('Priority ID must be a positive integer').optional(),
        movedToIndex: z.number().int().min(0, 'Index must be a positive integer').optional(),
    })
    .refine((data) => Object.keys(data).length > 0, {
        message: 'At least one field must be provided for update',
    })
