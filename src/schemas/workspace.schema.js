import { z } from 'zod'

// Schema for creating a new workspace
export const createWorkspaceSchema = z.object({
    name: z.string().min(1, 'Workspace name is required'),
    invite_link: z.string().optional(),
    img_url: z.string().url('Image URL must be a valid URL').optional(),
    plan: z.number().int().min(0).optional().default(0),
})

// Schema for updating an existing workspace
export const updateWorkspaceSchema = z
    .object({
        name: z.string().min(1, 'Workspace name is required').optional(),
        invite_link: z.string().optional(),
        img_url: z.string().url('Image URL must be a valid URL').optional(),
        plan: z.number().int().min(0).optional(),
    })
    .refine((data) => Object.keys(data).length > 0, {
        message: 'At least one field must be provided for update',
    })
export const inviteSchema = z.object({
    emails: z.array(z.string().email()).optional(), // optional for copy-link
    action: z.enum(['copy', 'email']),
})
