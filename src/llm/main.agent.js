import { StateGraph } from '@langchain/langgraph'
import { z } from 'zod'
import { ChatOpenAI } from '@langchain/openai'
import { continueConversation } from './agents/conversation.agent.js'
import getAISummary from './agents/summary.agent.js'
import { searchSimilarItems } from './utils/vector.utils.js'
import extractEntityType from './utils/extractEntity.utils.js'
import { getSession, updateSession } from './utils/sessionState.utils.js'

// === INTENT ROUTER SETUP ===

const routerSchema = z.object({
  step: z.enum(['conversation', 'summary', 'continue']),
})

const routerLLM = new ChatOpenAI({
  modelName: 'gpt-4o-mini',
  temperature: 0,
}).withStructuredOutput(routerSchema)

const RouterState = z.object({
  input: z.string(),
  decision: z.string().optional(),
  lastIntent: z.string().optional().nullable(),
  sessionId: z.string().optional(),
  history: z.array(
    z.object({
      role: z.enum(['user', 'ai', 'system']),
      content: z.string(),
    })
  ).optional().default([]),
})

// === ROUTER NODE ===

const routerNode = async (state) => {
  const decision = await routerLLM.invoke([
    {
      role: 'system',
      content: `
Classify the user's intent. Options:

- "conversation": idea, discussion, or building something.
- "summary": asks for progress, blockers, or reports.
- "continue": vague inputs like "go on", "continue", etc.

Only return: { "step": "conversation" | "summary" | "continue" }
      `.trim(),
    },
    { role: 'user', content: state.input },
  ])
  console.log(`[Router Decision]: "${state.input}" → ${decision.step}`)
  return { decision: decision.step }
}

// === HISTORY UTILS ===

const MAX_HISTORY_LENGTH = 20 // 10 exchanges (user+ai)
const TRIM_HISTORY_AT = 30    // If longer than this, summarize older part

const trimAndSummarizeHistory = async (history, res) => {
  if (history.length <= TRIM_HISTORY_AT) return history

  const toSummarize = history.slice(0, history.length - MAX_HISTORY_LENGTH)
  const recent = history.slice(-MAX_HISTORY_LENGTH)

  const summaryPrompt = toSummarize
    .map((msg) => `${msg.role.toUpperCase()}: ${msg.content}`)
    .join('\n')

  const summary = await getAISummary([], summaryPrompt, res)

  return [
    { role: 'system', content: `Summary of earlier discussion: ${summary}` },
    ...recent,
  ]
}

// === HANDLERS ===

const handleConversation = async (state, config) => {
  const res = config?.configurable?.res
  if (!res) throw new Error('Missing Express response object')

  const aiResponse = await continueConversation(state.input, state.sessionId, res)

  let updatedHistory = [
    ...(state.history || []),
    { role: 'user', content: state.input },
    { role: 'ai', content: aiResponse },
  ]

  updatedHistory = await trimAndSummarizeHistory(updatedHistory, res)

  console.log('[handleConversation] Appending to history:', updatedHistory.slice(-2))

  return {
    output: 'conversation complete',
    lastIntent: 'conversation',
    history: updatedHistory,
  }
}

const handleSummary = async (state, config) => {
  const res = config?.configurable?.res
  if (!res) throw new Error('Missing Express response object')

  const detectedType = extractEntityType(state.input)
  const filter = detectedType ? { must: [{ key: 'type', match: { value: detectedType } }] } : null

  const results = await searchSimilarItems(state.input, 10, filter)
  const summary = await getAISummary(results, state.input, res)

  let updatedHistory = [
    ...(state.history || []),
    { role: 'user', content: state.input },
    { role: 'ai', content: summary },
  ]

  updatedHistory = await trimAndSummarizeHistory(updatedHistory, res)

  console.log('[handleSummary] Appending to history:', updatedHistory.slice(-2))

  return {
    output: 'summary complete',
    lastIntent: 'summary',
    history: updatedHistory,
  }
}

// === ROUTING LOGIC ===

const routingDecision = (state) => {
  if (state.decision === 'continue') {
    return state.lastIntent === 'summary' ? 'handleSummary' : 'handleConversation'
  }
  return state.decision === 'summary' ? 'handleSummary' : 'handleConversation'
}

const mainRouterGraph = new StateGraph(RouterState)
  .addNode('routerNode', routerNode)
  .addNode('handleConversation', handleConversation)
  .addNode('handleSummary', handleSummary)
  .addEdge('__start__', 'routerNode')
  .addConditionalEdges('routerNode', routingDecision, ['handleConversation', 'handleSummary'])
  .addEdge('handleConversation', '__end__')
  .addEdge('handleSummary', '__end__')
  .compile()

// === AGENT ENTRYPOINT ===

const routeMainAgent = async ({ message, sessionId, res, lastIntent = null }) => {
  try {
    const session = getSession(sessionId)

    const stateInput = {
      input: message,
      sessionId,
      lastIntent,
      history: session.history || [],
    }

    console.log('[routeMainAgent] Incoming message:', message)
    console.log('[routeMainAgent] History before routing:', stateInput.history)

    const result = await mainRouterGraph.invoke(stateInput, {
      configurable: { res },
    })

    const updatedSession = {
      lastIntent: result.lastIntent,
      history: result.history,
    }

    console.log(`[routeMainAgent] Routing decision: ${result.lastIntent}`)
    console.log('[routeMainAgent] Updated history:', result.history)

    updateSession(sessionId, updatedSession)

    return result.lastIntent
  } catch (err) {
    console.error('[routeMainAgent] ERROR:', err)
    if (!res.writableEnded) {
      res.write(`event: error\n`)
      res.write(`data: ${JSON.stringify({ message: 'Internal agent error' })}\n\n`)
      res.end()
    }
  }
}

export default routeMainAgent