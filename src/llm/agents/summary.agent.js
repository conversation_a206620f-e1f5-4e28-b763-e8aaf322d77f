import { ChatOpenAI } from '@langchain/openai'
import { ChatPromptTemplate } from '@langchain/core/prompts'
import { StringOutputParser } from '@langchain/core/output_parsers'

/**
 * Streams an AI-generated summary for search results.
 * Uses SSE format so the frontend can consume chunked data correctly.
 */
const getAISummary = async(results, query, res) => {
  try {
    // Set SSE headers
    res.setHeader('Content-Type', 'text/event-stream')
    res.setHeader('Cache-Control', 'no-cache')
    res.setHeader('Connection', 'keep-alive')

    // Format search results for prompt
    const formattedResults = results
      .map((result, index) => {
        const { payload, score } = result
        let formattedItem = `Result ${index + 1} (Score: ${score.toFixed(2)}):\n`
        formattedItem += `Type: ${payload.type}\n`

        switch (payload.type) {
          case 'workspace':
            formattedItem += `Name: ${payload.name}\nPlan: ${payload.plan}\n`
            break
          case 'project':
            formattedItem += `Name: ${payload.name}\nWorkspace: ${payload.workspace_name}\nStatus: ${payload.status}\n`
            if (payload.overview) formattedItem += `Overview: ${payload.overview}\n`
            break
          case 'feature':
            formattedItem += `Feature Code: ${payload.feat_code}\nTitle: ${payload.title}\nProject: ${payload.project_name}\nStatus: ${payload.status}\nDraft: ${payload.draft ? 'Yes' : 'No'}\n`
            if (payload.description) formattedItem += `Description: ${payload.description}\n`
            break
          case 'task':
            formattedItem += `Task Code: ${payload.short_code}\nTitle: ${payload.title}\nFeature: ${payload.feature_code} - ${payload.feature_title}\nProject: ${payload.project_name}\nStatus: ${payload.status}\nAssigned to: ${payload.assignee}\nDetails: ${payload.text}\n`
            if (payload.time_estimate_hrs) formattedItem += `Time estimate: ${payload.time_estimate_hrs} hours\n`
            if (payload.start_date) formattedItem += `Start date: ${payload.start_date}\n`
            if (payload.due_date) formattedItem += `Due date: ${payload.due_date}\n`
            if (payload.description) formattedItem += `Description: ${payload.description}\n`
            break
        }

        return formattedItem
      })
      .join('\n\n')

    // Define system and user prompts
    const systemTemplate = `Answer the question based strictly on the provided data. Always include factual details such as status, assigned users, and other relevant entities if available. Be concise, clear, and friendly. Use professional but light emojis to keep the tone engaging (✨😊🔧📌✅). 

When users ask about people (e.g., “Who is working on...”), always mention assigned users by name if present. If no one is assigned, state that clearly. Do not infer or assume missing information.

Keep the tone conversational but informative. Also, provide a simple analysis of the data.`
    
    const humanTemplate = `Query: "{query}"

Search Results:

{results}

Respond to the query based on the search results. Add professional emojis from time to time.`

    const prompt = ChatPromptTemplate.fromMessages([
      ['system', systemTemplate],
      ['human', humanTemplate],
    ])

    const llm = new ChatOpenAI({
      modelName: 'gpt-4o-mini',
      temperature: 0.3,
      maxTokens: 500,
      streaming: true,
    })

    const chain = prompt.pipe(llm).pipe(new StringOutputParser())

    const stream = await chain.stream({
      query,
      results: formattedResults,
    })

    let responseContent = ''

    for await (const chunk of stream) {
      res.write(`event: chunk\n`)
      res.write(`data: ${JSON.stringify({ content: chunk })}\n\n`)
      responseContent += chunk
    }

    res.write(`event: complete\n`)
    res.write(`data: ${JSON.stringify({ message: 'Stream completed' })}\n\n`)
    res.end()

    return responseContent
  } catch (error) {
    console.error('[getAISummary] Error:', error)
    res.write(`event: error\n`)
    res.write(`data: ${JSON.stringify({ message: 'Summary generation failed' })}\n\n`)
    res.end()
  }
}

export default getAISummary;
