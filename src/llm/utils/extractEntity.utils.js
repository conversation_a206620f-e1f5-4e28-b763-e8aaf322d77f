/**
 * Helper: extractEntityType from natural language
 */
const extractEntityType = (query) => {
    if (!query || typeof query !== 'string') return null

    const normalizedQuery = query.toLowerCase().trim()

    const entityKeywords = {
        task: {
            primary: ['task', 'tasks', 'todo', 'todos', 'assignment', 'assignments'],
            secondary: ['assigned to', 'responsible for', 'doing', 'complete', 'finish', 'deadline', 'due', 'deliverable'],
        },
        project: {
            primary: ['project', 'projects', 'initiative', 'initiatives'],
            secondary: ['program', 'programs', 'campaign', 'campaigns'],
        },
        feature: {
            primary: ['feature', 'features', 'functionality', 'functionalities'],
            secondary: ['component', 'components', 'module', 'modules', 'enhancement', 'enhancements', 'capability', 'capabilities'],
        },
        workspace: {
            primary: ['workspace', 'workspaces', 'team', 'teams'],
            secondary: ['group', 'groups', 'department', 'departments', 'organization', 'organizations', 'space', 'spaces', 'environment', 'environments'],
        },
    }

    const contextPhrases = ['working on', 'involved in', 'part of', 'assigned to']

    const scores = {}

    Object.keys(entityKeywords).forEach((entityType) => {
        scores[entityType] = 0

        entityKeywords[entityType].primary.forEach((keyword) => {
            const regex = new RegExp(`\\b${keyword}\\b`, 'gi')
            const matches = normalizedQuery.match(regex)
            if (matches) scores[entityType] += matches.length * 3
        })

        entityKeywords[entityType].secondary.forEach((keyword) => {
            const regex = new RegExp(`\\b${keyword}\\b`, 'gi')
            const matches = normalizedQuery.match(regex)
            if (matches) scores[entityType] += matches.length * 2
        })

        const hasContextOnly = contextPhrases.some((phrase) => new RegExp(`\\b${phrase}\\b`, 'gi').test(normalizedQuery))
        const hasPrimaryKeyword = entityKeywords[entityType].primary.some((keyword) => new RegExp(`\\b${keyword}\\b`, 'gi').test(normalizedQuery))

        if (hasContextOnly && !hasPrimaryKeyword && scores[entityType] === 0) {
            // leave score at 0
        }
    })

    const maxScore = Math.max(...Object.values(scores))

    if (maxScore === 0) return null

    return Object.keys(scores).find((key) => scores[key] === maxScore)
}

export default extractEntityType;