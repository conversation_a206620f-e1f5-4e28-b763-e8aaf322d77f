import { QdrantClient } from '@qdrant/js-client-rest'
import { OpenAI } from 'openai'
import { v4 as uuidv4 } from 'uuid'

// Initialize OpenAI and Qdrant clients
const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
})

const qdrantClient = new QdrantClient({
    url: process.env.QDRANT_URL || 'http://localhost:6333',
    apiKey: process.env.QDRANT_API_KEY,
})

const COLLECTION_NAME = process.env.QDRANT_COLLECTION || 'project_management_data'
const VECTOR_SIZE = 1536

/**
 * Ensure the collection exists
 */
async function ensureCollectionExists() {
    try {
        await qdrantClient.getCollection(COLLECTION_NAME)
        console.log(`Collection '${COLLECTION_NAME}' exists`)
    } catch (error) {
        console.log(`Creating collection '${COLLECTION_NAME}'...`)
        await qdrantClient.createCollection(COLLECTION_NAME, {
            vectors: {
                size: VECTOR_SIZE,
                distance: 'Cosine',
            },
        })
        console.log(`Collection '${COLLECTION_NAME}' created successfully`)
    }
}

/**
 * Get embedding for text using OpenAI
 * @param {string} text - Text to get embedding for
 * @returns {Promise<Array<number>>} - The embedding vector
 */
async function getEmbedding(text) {
    try {
        const response = await openai.embeddings.create({
            model: 'text-embedding-3-small',
            input: text,
        })
        return response.data[0].embedding
    } catch (error) {
        console.error('1:', error)
        throw error
    }
}

/**
 * Search for similar items in the vector database
 * @param {string} query - Search query
 * @param {number} limit - Maximum number of results to return
 * @param {object} filter - Filter for search results
 * @returns {Promise<Array>} - Search results
 */
async function searchSimilarItems(query, limit = 15, filter = null) {
    try {
        const embedding = await getEmbedding(query)
        const searchResult = await qdrantClient.search(COLLECTION_NAME, {
            vector: embedding,
            limit: limit,
            filter: filter,
            with_payload: true,
            with_vectors: false,
        })
        return searchResult
    } catch (error) {
        console.error('Error searching similar items:', error)
        throw error
    }
}

/**
 * Entity text generators for different types
 */
const entityTextGenerators = {
    project: (data) => {
        const { name, overview, workspace, status, stack, creator, team = [] } = data
        const projectDescription = `
            The project is titled "${name}".
            It is described as: ${overview || 'No overview provided'}.
            This project is part of the "${workspace?.name || 'Unknown'}" workspace.
            Currently, its status is ${status?.label || 'Unknown'}.

            The technology stack used in this project includes: ${stack ? Object.values(stack).join(', ') : 'Not specified'}.

            The project was created by ${creator?.first_name ? `${creator.first_name} ${creator.last_name}` : 'an unknown user'}.
            The team working on this project consists of: ${
                team.length > 0
                    ? team.map((member) => `${member.first_name} ${member.last_name}`).join(', ')
                    : 'no team members have been assigned yet'
            }.
        `
        return projectDescription
    },

    feature: (data) => {
        const { feat_code, title, description, project, status, creator, draft, tasks = [] } = data
        const featureDescription = `
        Feature ${feat_code}: ${title}.
        Description: ${description || 'No description provided'}.
        This feature belongs to the project "${project?.name || 'Unknown'}".
        The current status of the feature is "${status?.label || 'Unknown'}".
        Is this feature a draft? ${draft ? 'Yes' : 'No'}.

        It was created by ${creator?.first_name ? `${creator.first_name} ${creator.last_name}` : 'an unknown user'}.
        There are ${tasks.length} associated task(s).

        ${
            tasks.length > 0
                ? `Here are up to three of the most recent tasks: ${tasks
                      .slice(0, 3)
                      .map((task) => task.title)
                      .join(', ')}.`
                : ''
        }
        `

        return featureDescription
    },

    task: (data) => {
        const {
            short_code,
            title,
            description,
            feature,
            project,
            status,
            assignedUser,
            department,
            priority,
            time_estimate_hrs,
            start_date,
            due_date,
            creator,
            parentTasks,
            dependentTasks,
        } = data

        const taskDescription = `
        Task ${short_code}: ${title}.
        This task is described as: ${description || 'No description provided'}.

        It is part of the feature "${feature?.feat_code || 'Unknown'}" titled "${feature?.title || 'Unknown'}",
        and is associated with the project "${project?.name || 'Unknown'}".

        The task is currently in "${status?.label || 'Unknown'}" status, with a priority level of "${
            priority?.label || 'Medium'
        }".
        It falls under the "${department?.label || 'Unknown'}" department and is estimated to take approximately ${
            time_estimate_hrs || 'an unspecified amount of'
        } hours to complete.

        ${
            assignedUser?.length > 0
                ? `The following team member(s) are assigned to work on this task: ${assignedUser
                      .map((user) => `${user.first_name} ${user.last_name}`)
                      .join(', ')}. Each of them is responsible for completing this task.`
                : 'No team members have been assigned to this task yet.'
        }

        This task was created by ${creator?.first_name ? `${creator.first_name} ${creator.last_name}` : 'an unknown user'}.
        The planned start date is ${start_date || 'not set'}, and the due date is ${due_date || 'not set'}.
        ${
            dependentTasks.length > 0
                ? `This task is a parent of the following task(s): ${dependentTasks
                      .map(
                          (task) =>
                              `${task.short_code} : ${task.title} , assigned to ${task.taskAssignees
                                  .map((user) => `${user.first_name} ${user.last_name}`)
                                  .join(', ')}, this task is in ${task.taskStatus.label} status`
                      )
                      .join(', ')}.`
                : ''
        }
        ${
            parentTasks.length > 0
                ? `This task is a dependent on the following task(s): ${parentTasks
                      .map(
                          (task) =>
                              `${task.short_code} : ${task.title} , assigned to ${task.taskAssignees
                                  .map((user) => `${user.first_name} ${user.last_name}`)
                                  .join(', ')}, this task is in ${task.taskStatus.label} status`
                      )
                      .join(', ')}.`
                : ''
        }
        `

        return taskDescription
    },

    workspace: (data) => {
        const { name, description, users = [], projects = [] } = data

        const workspaceDescription = `
        Workspace: ${name}.
        Description: ${description || 'No description provided'}.

        This workspace includes the following members: ${
            users.length > 0
                ? users.map((user) => `${user.first_name} ${user.last_name}`).join(', ')
                : 'No members have been added yet'
        }.

        There are currently ${projects.length} project(s) in this workspace.
        ${
            projects.length > 0
                ? `Some of the most recent projects include: ${projects
                      .slice(0, 3)
                      .map((project) => project.name)
                      .join(', ')}.`
                : ''
        }
        `
        return workspaceDescription
    },

    user: (data) => {
        const { first_name, last_name, email, workspaces = [], projects = [], assignedTasks = [] } = data

        const userDescription = `
        User: ${first_name} ${last_name}.
        Email address: ${email || 'Not provided'}.

        This user is a member of the following workspaces: ${
            workspaces.length > 0 ? workspaces.map((ws) => ws.name).join(', ') : 'No workspaces assigned'
        }.

        They are involved in the following projects: ${
            projects.length > 0 ? projects.map((project) => project.name).join(', ') : 'No projects assigned'
        }.

        Total number of tasks assigned to this user: ${assignedTasks.length}.
        `
        return userDescription
    },
}

/**
 * Payload generators for different entity types
 */
const payloadGenerators = {
    project: (data, text) => ({
        type: 'project',
        id: data.id,
        name: data.name,
        overview: data.overview,
        workspace_id: data.workspace_id,
        workspace_name: data.workspace?.name,
        status_id: data.status_id,
        status: data.status?.label,
        stack: data.stack,
        created_by: data.created_by,
        creator_name: data.creator?.first_name ? `${data.creator.first_name} ${data.creator.last_name}` : null,
        team_count: data.team?.length || 0,
        text: text,
        updated_at: new Date().toISOString(),
    }),

    feature: (data, text) => ({
        type: 'feature',
        id: data.id,
        feat_code: data.feat_code,
        title: data.title,
        description: data.description,
        project_id: data.project_id,
        project_name: data.project?.name,
        status_id: data.status_id,
        status: data.status?.label,
        draft: data.draft,
        created_by: data.created_by,
        creator_name: data.creator?.first_name ? `${data.creator.first_name} ${data.creator.last_name}` : null,
        tasks_count: data.tasks?.length || 0,
        text: text,
        updated_at: new Date().toISOString(),
    }),

    task: (data, text) => ({
        type: 'task',
        id: data.id,
        short_code: data.short_code,
        title: data.title,
        description: data.description,
        feature_id: data.feat_id || data.feature_id,
        feature_code: data.feature?.feat_code,
        feature_title: data.feature?.title,
        project_id: data.project?.id,
        project_name: data.project?.name,
        status_id: data.status_id,
        status: data.status?.label,
        priority_id: data.priority_id,
        priority: data.priority?.label,
        department_id: data.department_id || data.department,
        department: data.department?.label,
        assignee_id:
            Array.isArray(data.assignedUser) && data.assignedUser?.length > 0 ? data.assignedUser.map((user) => user.id) : null,
        assignee_name:
            data.assignedUser?.length > 0
                ? data.assignedUser.map((user) => `${user.first_name} ${user.last_name}`).join(', ')
                : null,
        created_by: data.created_by,
        creator_name: data.creator?.first_name ? `${data.creator.first_name} ${data.creator.last_name}` : null,
        time_estimate_hrs: data.time_estimate_hrs,
        start_date: data.start_date,
        due_date: data.due_date,
        text: text,
        updated_at: new Date().toISOString(),
    }),

    workspace: (data, text) => ({
        type: 'workspace',
        id: data.id,
        name: data.name,
        description: data.description,
        members_count: data.users?.length || 0,
        projects_count: data.projects?.length || 0,
        text: text,
        updated_at: new Date().toISOString(),
    }),

    user: (data, text) => ({
        type: 'user',
        id: data.id,
        first_name: data.first_name,
        last_name: data.last_name,
        email: data.email,
        workspaces_count: data.workspaces?.length || 0,
        projects_count: data.projects?.length || 0,
        assigned_tasks_count: data.assignedTasks?.length || 0,
        text: text,
        updated_at: new Date().toISOString(),
    }),
}

/**
 * Universal vector management class
 */
class VectorManager {
    constructor() {
        this.ensureCollectionExists().catch((err) => {
            console.error('Failed to ensure collection exists:', err)
        })
    }

    async ensureCollectionExists() {
        return await ensureCollectionExists()
    }

    /**
     * Upsert (create or update) a vector for any entity
     * @param {string} entityType - Type of entity (project, feature, task, workspace, user)
     * @param {object} entityData - Entity data with all relationships
     * @param {string} [customText] - Optional custom text override
     * @returns {Promise<void>}
     */
    async upsertVector(entityType, entityData, customText = null) {
        try {
            if (!entityTextGenerators[entityType]) {
                throw new Error(`Unsupported entity type: ${entityType}`)
            }

            // Generate text for embedding
            const text = customText || entityTextGenerators[entityType](entityData)

            // Generate embedding
            const vector = await getEmbedding(text)

            // Generate payload
            const payload = payloadGenerators[entityType](entityData, text)

            // Check for existing vectors
            const existingVectors = await this.findVectorsByEntity(entityType, entityData.id)

            if (existingVectors.length > 0) {
                // Update existing vectors
                const updatePromises = existingVectors.map((point) =>
                    qdrantClient.upsert(COLLECTION_NAME, {
                        wait: true,
                        points: [
                            {
                                id: point.id,
                                vector: vector,
                                payload: payload,
                            },
                        ],
                    })
                )
                await Promise.all(updatePromises)
                console.log(`Updated ${existingVectors.length} existing vectors for ${entityType} ${entityData.id}`)
            } else {
                // Create new vector
                await qdrantClient.upsert(COLLECTION_NAME, {
                    wait: true,
                    points: [
                        {
                            id: uuidv4(),
                            vector: vector,
                            payload: payload,
                        },
                    ],
                })
                console.log(`Created new vector for ${entityType} ${entityData.id}`)
            }
        } catch (error) {
            console.error(`Error upserting ${entityType} vector:`, error)
            throw error
        }
    }

    /**
     * Delete all vectors for an entity
     * @param {string} entityType - Type of entity
     * @param {number|string} entityId - Entity ID
     * @returns {Promise<void>}
     */
    async deleteVector(entityType, entityId) {
        try {
            const existingVectors = await this.findVectorsByEntity(entityType, entityId)

            if (existingVectors.length > 0) {
                const pointIds = existingVectors.map((point) => point.id)
                await qdrantClient.delete(COLLECTION_NAME, {
                    wait: true,
                    points: pointIds,
                })
                console.log(`Deleted ${pointIds.length} vectors for ${entityType} ${entityId}`)
            } else {
                console.log(`No vectors found for ${entityType} ${entityId}`)
            }
        } catch (error) {
            console.error(`Error deleting ${entityType} vector:`, error)
            throw error
        }
    }

    /**
     * Find existing vectors for an entity
     * @param {string} entityType - Type of entity
     * @param {number|string} entityId - Entity ID
     * @returns {Promise<Array>}
     */
    async findVectorsByEntity(entityType, entityId) {
        try {
            const searchResult = await qdrantClient.scroll(COLLECTION_NAME, {
                filter: {
                    must: [
                        { key: 'type', match: { value: entityType } },
                        { key: 'id', match: { value: entityId } },
                    ],
                },
                limit: 10,
                with_payload: true,
                with_vectors: false,
            })

            return searchResult.points || []
        } catch (error) {
            console.error(`Error finding vectors for ${entityType} ${entityId}:`, error)
            return []
        }
    }

    /**
     * Batch upsert multiple entities
     * @param {Array<{type: string, data: object}>} entities - Array of entities to upsert
     * @returns {Promise<void>}
     */
    async batchUpsert(entities) {
        const promises = entities.map((entity) =>
            this.upsertVector(entity.type, entity.data).catch((err) => {
                console.error(`Failed to upsert ${entity.type} ${entity.data.id}:`, err)
                return null
            })
        )

        await Promise.all(promises)
        console.log(`Batch upserted ${entities.length} entities`)
    }

    /**
     * Search for similar entities
     * @param {string} query - Search query
     * @param {number} limit - Maximum number of results
     * @param {string} [entityType] - Filter by entity type
     * @param {object} [additionalFilters] - Additional filters
     * @returns {Promise<Array>}
     */
    async searchSimilar(query, limit = 10, entityType = null, additionalFilters = {}) {
        try {
            const vector = await getEmbedding(query)

            const filters = { must: [] }

            if (entityType) {
                filters.must.push({ key: 'type', match: { value: entityType } })
            }

            // Add additional filters
            Object.entries(additionalFilters).forEach(([key, value]) => {
                filters.must.push({ key, match: { value } })
            })

            const searchResult = await qdrantClient.search(COLLECTION_NAME, {
                vector: vector,
                limit: limit,
                filter: filters.must.length > 0 ? filters : null,
                with_payload: true,
                with_vectors: false,
            })

            return searchResult || []
        } catch (error) {
            console.error('Error searching similar entities:', error)
            throw error
        }
    }

    /**
     * Get collection statistics
     * @returns {Promise<object>}
     */
    async getStats() {
        try {
            const scrollResult = await qdrantClient.scroll(COLLECTION_NAME, {
                limit: 1000,
                with_payload: { include: ['type'] },
                with_vectors: false,
            })

            const points = scrollResult.points || []
            const totalCount = points.length

            const typeCounts = points.reduce((counts, point) => {
                const type = point.payload?.type || 'unknown'
                counts[type] = (counts[type] || 0) + 1
                return counts
            }, {})

            return {
                totalCount,
                typeCounts,
                collectionName: COLLECTION_NAME,
            }
        } catch (error) {
            console.error('Error getting collection stats:', error)
            throw error
        }
    }

    /**
     * Safe wrapper for vector operations (non-blocking)
     * @param {Function} operation - Vector operation to perform
     * @param {string} description - Description for logging
     * @returns {Promise<void>}
     */
    async safeExecute(operation, description) {
        operation().catch((err) => {
            console.error(`Failed ${description}:`, err)
        })
    }
}

// Create singleton instance
const vectorManager = new VectorManager()

export { VectorManager, vectorManager, entityTextGenerators, payloadGenerators, searchSimilarItems }
