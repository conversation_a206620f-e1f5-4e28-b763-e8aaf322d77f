const sessionStore = new Map()

export function getSession(sessionId) {
  if (!sessionStore.has(sessionId)) {
    sessionStore.set(sessionId, {
      lastIntent: null,
      history: [],
    })
  }
  return sessionStore.get(sessionId)
}

export function updateSession(sessionId, updates) {
  const session = getSession(sessionId)
  sessionStore.set(sessionId, { ...session, ...updates })
}

export function clearSession(sessionId) {
  sessionStore.delete(sessionId)
}