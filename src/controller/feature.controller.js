import { Op } from 'sequelize'
import Feature from '../../db/models/feature.js'
import Project from '../../db/models/project.js'
import Status from '../../db/models/status.js'
import User from '../../db/models/user.js'
import { AppError } from '../utils/app-error.js'
import { catchAsync } from '../utils/catch-async.js'
import { vectorManager } from '../llm/utils/vector.utils.js'

/**
 * Create a new feature
 */
export const createFeature = catchAsync(async (req, res, next) => {
    const { feat_code, title, description, draft, project_id, status_id } = req.body
    const userId = req.user.id

    // Check if project exists
    const project = await Project.findByPk(project_id)
    if (!project) {
        return next(new AppError('Project not found', 404))
    }

    // Check if status exists
    const status = await Status.findByPk(status_id)
    if (!status) {
        return next(new AppError('Status not found', 404))
    }

    // Create the feature
    const newFeature = await Feature.create({
        feat_code,
        title,
        description,
        draft,
        project_id,
        status_id,
        created_by: userId,
    })

    // Fetch the feature with associations for vector creation
    const featureWithAssociations = await Feature.findByPk(newFeature.id, {
        include: [
            {
                model: Project,
                attributes: ['id', 'name'],
            },
            {
                model: Status,
                attributes: ['id', 'short_code', 'label', 'colour'],
            },
            {
                model: User,
                as: 'creator',
                attributes: ['id', 'first_name', 'last_name'],
            },
        ],
    })

    // Update vector (non-blocking)
    vectorManager.safeExecute(
        () =>
            vectorManager.upsertVector('feature', {
                id: featureWithAssociations.id,
                feat_code: featureWithAssociations.feat_code,
                title: featureWithAssociations.title,
                description: featureWithAssociations.description,
                draft: featureWithAssociations.draft,
                project_id: featureWithAssociations.project_id,
                status_id: featureWithAssociations.status_id,
                created_by: featureWithAssociations.created_by,
                project: featureWithAssociations.Project,
                status: featureWithAssociations.Status,
                creator: featureWithAssociations.creator,
                tasks: [], // No tasks initially
            }),
        'create feature vector'
    )

    return res.status(201).json({
        status: 'success',
        data: featureWithAssociations,
    })
})

/**
 * Get all features with optional filtering
 */
export const getAllFeatures = catchAsync(async (req, res, next) => {
    const { project_id, status_id, draft } = req.query

    // Build filter conditions
    const whereConditions = {}

    if (project_id) {
        whereConditions.project_id = project_id
    }

    if (status_id) {
        whereConditions.status_id = status_id
    }

    if (draft !== undefined) {
        whereConditions.draft = draft === 'true'
    }

    // Get features with associations
    const features = await Feature.findAll({
        where: whereConditions,
        include: [
            {
                model: Project,
                attributes: ['id', 'name'],
            },
            {
                model: Status,
                attributes: ['id', 'short_code', 'label', 'colour'],
            },
            {
                model: User,
                as: 'creator',
                attributes: ['id', 'first_name', 'last_name'],
            },
        ],
        order: [['created_at', 'DESC']],
    })

    return res.status(200).json({
        status: 'success',
        results: features.length,
        data: features,
    })
})

/**
 * Get a single feature by ID
 */
export const getFeatureById = catchAsync(async (req, res, next) => {
    const { id } = req.params

    const feature = await Feature.findByPk(id, {
        include: [
            {
                model: Project,
                attributes: ['id', 'name'],
            },
            {
                model: Status,
                attributes: ['id', 'short_code', 'label', 'colour'],
            },
            {
                model: User,
                as: 'creator',
                attributes: ['id', 'first_name', 'last_name'],
            },
        ],
    })

    if (!feature) {
        return next(new AppError('Feature not found', 404))
    }

    return res.status(200).json({
        status: 'success',
        data: feature,
    })
})

/**
 * Update a feature
 */
export const updateFeature = catchAsync(async (req, res, next) => {
    const { id } = req.params
    const { feat_code, title, description, draft, project_id, status_id } = req.body

    // Find the feature
    const feature = await Feature.findByPk(id)
    if (!feature) {
        return next(new AppError('Feature not found', 404))
    }

    // Check if project exists if project_id is provided
    if (project_id) {
        const project = await Project.findByPk(project_id)
        if (!project) {
            return next(new AppError('Project not found', 404))
        }
    }

    // Check if status exists if status_id is provided
    if (status_id) {
        const status = await Status.findByPk(status_id)
        if (!status) {
            return next(new AppError('Status not found', 404))
        }
    }

    // Update the feature
    await feature.update({
        feat_code: feat_code !== undefined ? feat_code : feature.feat_code,
        title: title !== undefined ? title : feature.title,
        description: description !== undefined ? description : feature.description,
        draft: draft !== undefined ? draft : feature.draft,
        project_id: project_id !== undefined ? project_id : feature.project_id,
        status_id: status_id !== undefined ? status_id : feature.status_id,
    })

    // Fetch the updated feature with associations for vector update
    const updatedFeatureWithAssociations = await Feature.findByPk(id, {
        include: [
            {
                model: Project,
                attributes: ['id', 'name'],
            },
            {
                model: Status,
                attributes: ['id', 'short_code', 'label', 'colour'],
            },
            {
                model: User,
                as: 'creator',
                attributes: ['id', 'first_name', 'last_name'],
            },
        ],
    })

    // Update vector (non-blocking)
    vectorManager.safeExecute(
        () =>
            vectorManager.upsertVector('feature', {
                id: updatedFeatureWithAssociations.id,
                feat_code: updatedFeatureWithAssociations.feat_code,
                title: updatedFeatureWithAssociations.title,
                description: updatedFeatureWithAssociations.description,
                draft: updatedFeatureWithAssociations.draft,
                project_id: updatedFeatureWithAssociations.project_id,
                status_id: updatedFeatureWithAssociations.status_id,
                created_by: updatedFeatureWithAssociations.created_by,
                project: updatedFeatureWithAssociations.Project,
                status: updatedFeatureWithAssociations.Status,
                creator: updatedFeatureWithAssociations.creator,
                tasks: [], // Might want to fetch related tasks later
            }),
        'update feature vector'
    )

    return res.status(200).json({
        status: 'success',
        data: updatedFeatureWithAssociations,
    })
})

/**
 * Delete a feature
 */
export const deleteFeature = catchAsync(async (req, res, next) => {
    const { id } = req.params

    const feature = await Feature.findByPk(id)
    if (!feature) {
        return next(new AppError('Feature not found', 404))
    }

    // Delete vector (non-blocking)
    vectorManager.safeExecute(
        () => vectorManager.deleteVector('feature', id),
        'delete feature vector'
    )

    // Soft delete the feature (using paranoid option)
    await feature.destroy()

    return res.status(204).json({
        status: 'success',
        data: null,
    })
})