import { Op } from 'sequelize'
import Workspace from '../../db/models/workspace.js'
import UserWorkspaceMapping from '../../db/models/user-workspace-mapping.js'
import User from '../../db/models/user.js'
import { AppError } from '../utils/app-error.js'
import { catchAsync } from '../utils/catch-async.js'
import { v4 as uuidv4 } from 'uuid'
import crypto from 'crypto'
import Invitation from '../../db/models/invitation.js'
import { sendEmail } from '../utils/email.js'
import jwt from 'jsonwebtoken'
import sequelize from '../../config/db.config.js'

/**
 * Create a new workspace
 */
export const createWorkspace = catchAsync(async (req, res, next) => {
    const { name, img_url, plan, role } = req.body
    const userId = req.user.id

    const invite_link = req.body.invite_link || `invite-${uuidv4()}`
    const transaction = await sequelize.transaction()

    try {
        const newWorkspace = await Workspace.create(
            {
                name,
                invite_link,
                img_url,
                plan,
            },
            { transaction }
        )

        // Add the creator as a member of the workspace
        await UserWorkspaceMapping.create(
            {
                user_id: userId,
                workspace_id: newWorkspace.id,
            },
            { transaction }
        )

        if (role !== undefined) {
            await User.update(
                { role },
                {
                    where: { id: userId },
                },
                { transaction }
            )
        }

        await transaction.commit()

        return res.status(201).json({
            status: 'success',
            data: newWorkspace,
        })
    } catch (error) {
        await transaction.rollback()
        return next(new AppError(`Failed to create workspace: ${error.message}`, 500))
    }
})

/**
 * Get all workspaces for the current user
 */
export const getUserWorkspaces = catchAsync(async (req, res, next) => {
    const userId = req.user.id

    try {
        // Method 1: Find workspaces through User model (using the many-to-many relationship)
        const user = await User.findByPk(userId, {
            include: [
                {
                    model: Workspace,
                    attributes: ['id', 'name', 'invite_link', 'img_url', 'plan', 'created_at'],
                    through: { attributes: [] }, // Exclude join table attributes
                },
            ],
        })

        const workspaces = user.workspaces

        return res.status(200).json({
            status: 'success',
            results: workspaces.length,
            data: workspaces,
        })
    } catch (error) {
        console.log('Error using method 1, trying method 2:', error.message)

        // Method 2: Find workspaces through direct query
        // Get workspace IDs for the user
        const mappings = await UserWorkspaceMapping.findAll({
            where: { user_id: userId },
            attributes: ['workspace_id'],
        })

        const workspaceIds = mappings.map((mapping) => mapping.workspace_id)

        // Get the workspaces
        const workspaces = await Workspace.findAll({
            where: {
                id: { [Op.in]: workspaceIds },
            },
            attributes: ['id', 'name', 'invite_link', 'img_url', 'plan', 'created_at'],
        })

        return res.status(200).json({
            status: 'success',
            results: workspaces.length,
            data: workspaces,
        })
    }
})

/**
 * Get a single workspace by ID
 */
export const getWorkspaceById = catchAsync(async (req, res, next) => {
    const { id } = req.params
    const userId = req.user.id

    // Check if the user has access to this workspace
    const userWorkspaceMapping = await UserWorkspaceMapping.findOne({
        where: {
            user_id: userId,
            workspace_id: id,
        },
    })

    if (!userWorkspaceMapping) {
        return next(new AppError('Workspace not found or you do not have access to it', 404))
    }

    // Get the workspace
    const workspace = await Workspace.findByPk(id, {
        include: [
            {
                model: User,
                attributes: ['id', 'first_name', 'last_name', 'email'],
                through: { attributes: [] }, // Exclude join table attributes
            },
        ],
    })

    if (!workspace) {
        return next(new AppError('Workspace not found', 404))
    }

    return res.status(200).json({
        status: 'success',
        data: workspace,
    })
})

/**
 * Update a workspace
 */
export const updateWorkspace = catchAsync(async (req, res, next) => {
    const { id } = req.params
    const { name, invite_link, img_url, plan } = req.body
    const userId = req.user.id

    // Check if the user has access to this workspace
    const userWorkspaceMapping = await UserWorkspaceMapping.findOne({
        where: {
            user_id: userId,
            workspace_id: id,
        },
    })

    if (!userWorkspaceMapping) {
        return next(new AppError('Workspace not found or you do not have access to it', 404))
    }

    // Find the workspace
    const workspace = await Workspace.findByPk(id)
    if (!workspace) {
        return next(new AppError('Workspace not found', 404))
    }

    // Update the workspace
    await workspace.update({
        name: name !== undefined ? name : workspace.name,
        invite_link: invite_link !== undefined ? invite_link : workspace.invite_link,
        img_url: img_url !== undefined ? img_url : workspace.img_url,
        plan: plan !== undefined ? plan : workspace.plan,
    })

    return res.status(200).json({
        status: 'success',
        data: workspace,
    })
})

/**
 * Delete a workspace
 */
export const deleteWorkspace = catchAsync(async (req, res, next) => {
    const { id } = req.params
    const userId = req.user.id

    // Check if the user has access to this workspace
    const userWorkspaceMapping = await UserWorkspaceMapping.findOne({
        where: {
            user_id: userId,
            workspace_id: id,
        },
    })

    if (!userWorkspaceMapping) {
        return next(new AppError('Workspace not found or you do not have access to it', 404))
    }

    // Find the workspace
    const workspace = await Workspace.findByPk(id)
    if (!workspace) {
        return next(new AppError('Workspace not found', 404))
    }

    // Soft delete the workspace (using paranoid option)
    await workspace.destroy()

    return res.status(204).json({
        status: 'success',
        data: null,
    })
})

/**
 * Add a user to a workspace
 */
export const addUserToWorkspace = catchAsync(async (req, res, next) => {
    const { workspace_id, user_id } = req.body
    const currentUserId = req.user.id

    // Check if the current user has access to this workspace
    const userWorkspaceMapping = await UserWorkspaceMapping.findOne({
        where: {
            user_id: currentUserId,
            workspace_id,
        },
    })

    if (!userWorkspaceMapping) {
        return next(new AppError('Workspace not found or you do not have access to it', 404))
    }

    // Check if the user to be added exists
    const userToAdd = await User.findByPk(user_id)
    if (!userToAdd) {
        return next(new AppError('User not found', 404))
    }

    // Check if the user is already a member of the workspace
    const existingMapping = await UserWorkspaceMapping.findOne({
        where: {
            user_id,
            workspace_id,
        },
    })

    if (existingMapping) {
        return next(new AppError('User is already a member of this workspace', 400))
    }

    // Add the user to the workspace
    const newMapping = await UserWorkspaceMapping.create({
        user_id,
        workspace_id,
    })

    return res.status(201).json({
        status: 'success',
        data: newMapping,
    })
})

/**
 * Remove a user from a workspace
 */
export const removeUserFromWorkspace = catchAsync(async (req, res, next) => {
    const { workspace_id, user_id } = req.params
    const currentUserId = req.user.id

    // Check if the current user has access to this workspace
    const userWorkspaceMapping = await UserWorkspaceMapping.findOne({
        where: {
            user_id: currentUserId,
            workspace_id,
        },
    })

    if (!userWorkspaceMapping) {
        return next(new AppError('Workspace not found or you do not have access to it', 404))
    }

    // Find the mapping to remove
    const mappingToRemove = await UserWorkspaceMapping.findOne({
        where: {
            user_id,
            workspace_id,
        },
    })

    if (!mappingToRemove) {
        return next(new AppError('User is not a member of this workspace', 404))
    }

    // Don't allow removing yourself
    if (user_id === currentUserId) {
        return next(new AppError('You cannot remove yourself from the workspace', 400))
    }

    // Remove the user from the workspace
    await mappingToRemove.destroy()

    return res.status(204).json({
        status: 'success',
        data: null,
    })
})

export const inviteToWorkspace = async (req, res, next) => {
    const workspaceId = req.params.id
    const { emails, action } = req.body
    const invitedBy = req.user.id
    const TOKEN_EXPIRY_DAYS = 7
    const APP_URL = process.env.FRONTEND_URL || 'https://raydian.ai'

    if (action === 'email' && (!emails || emails?.length === 0)) {
        return next(new AppError('Emails are required for email action', 400))
    }
    if (action === 'email' && emails && emails.length > 0) {
        const existingMembers = await Workspace.findOne({
            where: { id: workspaceId },
            include: [
                {
                    model: User,
                    where: {
                        email: {
                            [Op.in]: emails,
                        },
                    },
                    through: { attributes: [] },
                },
            ],
        })

        if (existingMembers?.users.length > 0) {
            const alreadyInWorkspace = existingMembers.users.map((m) => m.email).filter(Boolean)
            return res.status(400).json({
                error: 'Some users are already in the workspace',
                emails: alreadyInWorkspace,
            })
            // return next(new AppError('Some users are already in the workspace', 400))
        }

        const invites = []

        for (const email of emails) {
            const inviteToken = crypto.randomBytes(32).toString('hex')
            const expiresAt = new Date(Date.now() + TOKEN_EXPIRY_DAYS * 24 * 60 * 60 * 1000) // 7 days from now

            const invite = await Invitation.create({
                workspace_id: workspaceId,
                email,
                invite_token: inviteToken,
                expires_at: expiresAt,
                status: 'pending',
                invited_by: invitedBy,
                created_at: new Date(),
                updated_at: new Date(),
            })

            invites.push(invite)
        }
        const inviteLinks = invites.map((invite) => ({
            email: invite.email,
            link: `${APP_URL}/join/${invite.invite_token}`,
        }))
        // Send emails
        for (const invite of inviteLinks) {
            try {
                await sendEmail({
                    to: invite.email,
                    subject: 'You have been invited to join a workspace',
                    text: `You have been invited to join a workspace. Click the link to accept the invitation: ${invite.link}. Invite expires at ${invite.expires_at}`,
                })
            } catch (error) {
                console.error('Failed to send email:', error)
            }
        }
        return res.status(200).json({
            success: true,
            message: 'Invitations sent',
        })
    } else {
        // action === 'copy' → no emails involved
        const inviteToken = crypto.randomBytes(32).toString('hex')
        const expiresAt = new Date(Date.now() + TOKEN_EXPIRY_DAYS * 24 * 60 * 60 * 1000)

        const invite = await Invitation.create({
            workspace_id: workspaceId,
            email: null,
            invite_token: inviteToken,
            expires_at: expiresAt,
            status: 'pending',
            invited_by: invitedBy,
            created_at: new Date(),
            updated_at: new Date(),
        })

        const inviteLink = `${APP_URL}/join/${invite.invite_token}`

        return res.status(200).json({
            success: true,
            invite_link: inviteLink,
        })
    }
}

export const getInviteDetails = async (req, res, next) => {
    const { token } = req.params

    let idToken = ''
    let tokenDetail
    let currentUser
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
        idToken = req.headers.authorization.split(' ')[1]
        tokenDetail = jwt.verify(idToken, process.env.JWT_ACCESS_SECRET)
        currentUser = await User.findByPk(tokenDetail.id, {
            attributes: { exclude: ['password', 'created_at', 'updated_at', 'deleted_at'] },
        })
    } else if (req.cookies?.auth?.accessToken) {
        idToken = req.cookies.auth.accessToken
    }

    const invite = await Invitation.findOne({
        where: { invite_token: token },
        include: [{ model: Workspace, attributes: ['id', 'name', 'img_url'] }],
    })

    if (!invite) {
        return next(new AppError('Invalid or expired invite token', 404))
    }

    const isExpired = new Date(invite.expires_at) < new Date()
    if (isExpired || invite.status === 'expired') {
        return next(new AppError('Invite has expired', 410))
    }

    let matchStatus = 'not-logged-in'
    if (currentUser) {
        if (invite.email === null || invite.email === currentUser.email) {
            matchStatus = 'match'
        } else {
            matchStatus = 'email-mismatch'
        }
    }

    return res.status(200).json({
        success: true,
        data: {
            workspace: invite.workspace,
            email: invite.email,
            matchStatus,
        },
    })
}

export const acceptInvite = async (req, res, next) => {
    const { token } = req.params
    const currentUser = req.user

    if (!currentUser) {
        return res.status(401).json({ error: 'Authentication required' })
    }

    const invite = await Invitation.findOne({ where: { invite_token: token } })

    if (!invite) {
        return next(new AppError('Invalid or expired invite token', 404))
    }

    const isExpired = new Date(invite.expires_at) < new Date()
    if (isExpired || invite.status === 'expired') {
        return next(new AppError('Invite has expired', 410))
    }

    if (invite.email && invite.email !== currentUser.email) {
        return next(new AppError('This invite is not for your email', 403))
    }

    // Step 4: Add user to workspace
    const userToAdd = await User.findByPk(currentUser.id)
    if (!userToAdd) {
        return next(new AppError('User not found', 404))
    }

    // Check if the user is already a member of the workspace
    const existingMapping = await UserWorkspaceMapping.findOne({
        where: {
            user_id: currentUser.id,
            workspace_id: invite.workspace_id,
        },
    })

    if (existingMapping) {
        return next(new AppError('User is already a member of this workspace', 400))
    }

    // Add the user to the workspace
    const newMapping = await UserWorkspaceMapping.create({
        user_id: currentUser.id,
        workspace_id: invite.workspace_id,
    })

    // Step 5: Mark invite as accepted
    invite.status = 'accepted'
    await invite.save()

    return res.status(200).json({ success: true, message: 'You have joined the workspace' })
}
