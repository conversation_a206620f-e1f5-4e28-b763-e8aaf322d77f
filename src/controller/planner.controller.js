import Status from '../../db/models/status.js'
import { catchAsync } from '../utils/catch-async.js'

export const getKanbanColumns = catchAsync(async (req, res) => {
    try {
        const columns = await Status.findAll({
            where: {
                kanban_column: true,
            },
            order: [['column_order', 'ASC']],
        })
        res.status(200).json({
            success: true,
            data: columns,
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error fetching kanban columns',
            error: error.message,
        })
    }
})
