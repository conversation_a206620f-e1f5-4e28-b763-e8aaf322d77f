import Task from '../../db/models/task.js'
import Feature from '../../db/models/feature.js'
import Status from '../../db/models/status.js'
import User from '../../db/models/user.js'
import Department from '../../db/models/department.js'
import { AppError } from '../utils/app-error.js'
import { catchAsync } from '../utils/catch-async.js'
import Priority from '../../db/models/priority.js'
import TaskUserMapping from '../../db/models/task-user-mapping.js'
import { Op } from 'sequelize'
import TaskDependencyMapping from '../../db/models/task-dependency-mapping.js'
import { vectorManager } from '../llm/utils/vector.utils.js'
import Project from '../../db/models/project.js'
import sequelize from '../../config/db.config.js'

/**
 * Create a new task
 */
export const createTask = catchAsync(async (req, res, next) => {
    const {
        feat_id,
        short_code,
        title,
        description,
        time_estimate_hrs,
        start_date,
        due_date,
        status,
        assignee,
        department,
        priority_id,
        bug,
        parent_task_short_code,
    } = req.body

    const userId = req.user.id

    // Check if feature exists
    if (feat_id) {
        const feature = await Feature.findByPk(feat_id)
        if (!feature) {
            return next(new AppError('Feature not found', 404))
        }
    }

    // Check if status exists
    if (status) {
        const statusRecord = await Status.findByPk(status)
        if (!statusRecord) {
            return next(new AppError('Status not found', 404))
        }
    }

    if (priority_id) {
        const priorityRecord = await Priority.findByPk(priority_id)
        if (!priorityRecord) {
            return next(new AppError('Priority not found', 404))
        }
    }

    // Check if assignees exist if provided
    if (assignee && assignee.length > 0) {
        const assigneeUsers = await User.findAll({
            where: {
                id: {
                    [Op.in]: assignee,
                },
            },
        })

        if (assigneeUsers.length !== assignee.length) {
            return next(new AppError('One or more assignees not found', 404))
        }
    }

    // Check if department exists if provided
    if (department) {
        const departmentRecord = await Department.findByPk(department)
        if (!departmentRecord) {
            return next(new AppError('Department not found', 404))
        }
    }

    // Check if parent task exists if provided
    let parentTask = null
    if (parent_task_short_code) {
        parentTask = await Task.findOne({
            where: { short_code: parent_task_short_code },
        })

        if (!parentTask) {
            return next(new AppError('Parent task not found', 404))
        }
    }

    // Create the task
    const newTask = await Task.create({
        feat_id,
        short_code,
        title,
        description,
        time_estimate_hrs,
        start_date,
        due_date,
        status_id: status,
        department,
        priority_id,
        bug: bug || false,
        created_by: userId,
    })

    // Create task-user mappings for assignees
    if (assignee && assignee.length > 0) {
        const taskUserMappings = assignee.map((userId) => ({
            task_id: newTask.id,
            user_id: userId,
        }))

        await TaskUserMapping.bulkCreate(taskUserMappings)
    }

    // Create task dependency if parent task is provided
    if (parentTask) {
        await TaskDependencyMapping.create({
            task_id: parentTask.id,
            dependant_task_id: newTask.id,
            dependency_level: 1, // Default dependency level
        })
    }

    // Return the newly created task with associations
    const taskWithAssociations = await Task.findByPk(newTask.id, {
        include: [
            {
                model: Feature,
                attributes: ['id', 'feat_code', 'title'],
                include: [
                    {
                        model: Project,
                        attributes: ['id', 'name'],
                    },
                ],
            },
            {
                model: Status,
                as: 'taskStatus',
                attributes: ['id', 'short_code', 'label', 'colour'],
            },
            {
                model: User,
                as: 'taskAssignees',
                through: { attributes: [] },
                attributes: ['id', 'first_name', 'last_name'],
            },
            {
                model: User,
                as: 'creator',
                attributes: ['id', 'first_name', 'last_name'],
            },
            {
                model: Department,
                as: 'taskDepartment',
                attributes: ['id', 'short_code', 'label', 'colour', 'icon'],
            },
            {
                model: Priority,
                as: 'taskPriority',
                attributes: ['id', 'label', 'level'],
            },
            {
                model: Task,
                as: 'parentTasks',
                through: { attributes: ['dependency_level'] },
                attributes: ['id', 'short_code', 'title'],
                include: [
                    {
                        model: User,
                        as: 'taskAssignees',
                        through: { attributes: [] },
                        attributes: ['id', 'first_name', 'last_name'],
                    },
                    {
                        model: Status,
                        as: 'taskStatus',
                        attributes: ['id', 'short_code', 'label'],
                    },
                    {
                        model: Department,
                        as: 'taskDepartment',
                        attributes: ['id', 'short_code', 'label', 'colour', 'icon'],
                    },
                ],
            },
            {
                model: Task,
                as: 'dependentTasks',
                through: { attributes: ['dependency_level'] },
                attributes: ['id', 'short_code', 'title'],
                include: [
                    {
                        model: User,
                        as: 'taskAssignees',
                        through: { attributes: [] },
                        attributes: ['id', 'first_name', 'last_name'],
                    },
                    {
                        model: Status,
                        as: 'taskStatus',
                        attributes: ['id', 'short_code', 'label'],
                    },
                    {
                        model: Department,
                        as: 'taskDepartment',
                        attributes: ['id', 'short_code', 'label', 'colour', 'icon'],
                    },
                ],
            },
        ],
    })
    // Update vector (non-blocking)
    vectorManager.safeExecute(
        () =>
            vectorManager.upsertVector('task', {
                id: taskWithAssociations.id,
                short_code: taskWithAssociations.short_code,
                title: taskWithAssociations.title,
                description: taskWithAssociations.description,
                feat_id: taskWithAssociations.feat_id,
                status_id: taskWithAssociations.status_id,
                assignee: taskWithAssociations.assignee,
                department: taskWithAssociations.department,
                priority_id: taskWithAssociations.priority_id,
                time_estimate_hrs: taskWithAssociations.time_estimate_hrs,
                start_date: taskWithAssociations.start_date,
                due_date: taskWithAssociations.due_date,
                created_by: taskWithAssociations.created_by,
                feature: taskWithAssociations.feature,
                project: taskWithAssociations.feature?.project,
                assignedUser: taskWithAssociations.assignedUser,
                status: taskWithAssociations.taskStatus,
                department: taskWithAssociations.taskDepartment,
                priority: taskWithAssociations.taskPriority,
                creator: taskWithAssociations.creator,
                parentTasks: taskWithAssociations.parentTasks,
                dependentTasks: taskWithAssociations.dependentTasks,
            }),
        'create task vector'
    )

    return res.status(201).json({
        status: 'success',
        data: taskWithAssociations,
    })
})

/**
 * Get all tasks with optional filtering
 */
export const getAllTasks = catchAsync(async (req, res, next) => {
    const { feat_id, status, assignee, department, priority, projects, bug, searchQuery, workspace_id } = req.query
    let isShowOnlyDelayedTasks = false
    // Build filter conditions
    const whereConditions = {}
    const featureWhereConditions = {}
    const projectWhereConditions = {}

    if (feat_id) {
        whereConditions.feat_id = feat_id
    }

    if (status) {
        const delayedStatus = await Status.findOne({
            where: {
                short_code: 'delayed', // Adjust this based on your status naming convention
            },
            attributes: ['id'],
        })
        const updatedstatus = status.filter((status) => status != delayedStatus.id)
        if (updatedstatus.length > 0) whereConditions.status_id = updatedstatus
        isShowOnlyDelayedTasks = status.includes(String(delayedStatus.id))
    }

    if (department) {
        whereConditions.department = department
    }

    if (priority) {
        whereConditions.priority_id = priority
    }

    if (bug) {
        whereConditions.bug = bug
    }

    if (searchQuery) {
        whereConditions[Op.or] = [
            {
                title: {
                    [Op.iLike]: `%${searchQuery}%`, // Case-insensitive search
                },
            },
            {
                description: {
                    [Op.iLike]: `%${searchQuery}%`, // Case-insensitive search
                },
            },
        ]
    }

    // Add project filter to feature conditions
    if (projects) {
        featureWhereConditions.project_id = Array.isArray(projects) ? { [Op.in]: projects } : projects
    }

    // Add workspace filter to project conditions
    if (workspace_id) {
        projectWhereConditions.workspace_id = workspace_id
    }

    if (isShowOnlyDelayedTasks) {
        const now = new Date()
        whereConditions.due_date = { [Op.lt]: now }
    }

    // Get tasks with associations
    const tasks = await Task.findAll({
        where: whereConditions,
        include: [
            {
                model: Feature,
                attributes: ['id', 'feat_code', 'title', 'project_id'],
                where: Object.keys(featureWhereConditions).length > 0 ? featureWhereConditions : undefined,
                required: workspace_id ? true : false,
                include: [
                    {
                        model: Project,
                        attributes: ['id', 'name', 'workspace_id'],
                        where: Object.keys(projectWhereConditions).length > 0 ? projectWhereConditions : undefined,
                        required: workspace_id ? true : false,
                    },
                ],
            },
            {
                model: Status,
                as: 'taskStatus',
                attributes: ['id', 'short_code', 'label', 'colour'],
            },
            {
                model: User,
                as: 'taskAssignees',
                through: { attributes: [] },
                attributes: ['id', 'first_name', 'last_name', 'img_url'],
                ...(assignee
                    ? {
                          where: {
                              id: Array.isArray(assignee) ? { [Op.in]: assignee } : assignee,
                          },
                      }
                    : {}),
            },
            {
                model: User,
                as: 'creator',
                attributes: ['id', 'first_name', 'last_name', 'img_url'],
            },
            {
                model: Department,
                as: 'taskDepartment',
                attributes: ['id', 'short_code', 'label', 'colour', 'icon'],
            },
            {
                model: Priority,
                as: 'taskPriority',
                attributes: ['id', 'label', 'level', 'color'],
            },
            {
                model: Task,
                as: 'dependentTasks',
                through: { attributes: ['dependency_level'] },
                attributes: ['id', 'short_code', 'title', 'status_id'],
                include: [
                    {
                        model: Status,
                        as: 'taskStatus',
                        attributes: ['id', 'short_code', 'label'],
                    },
                    {
                        model: Department,
                        as: 'taskDepartment',
                        attributes: ['id', 'short_code', 'label', 'colour', 'icon'],
                    },
                ],
            },
            {
                model: Task,
                as: 'parentTasks',
                through: { attributes: ['dependency_level'] },
                attributes: ['id', 'short_code', 'title', 'status_id'],
                include: [
                    {
                        model: Status,
                        as: 'taskStatus',
                        attributes: ['id', 'short_code', 'label'],
                    },
                ],
            },
        ],
        order: [['kanban_position'], ['updated_at', 'DESC']],
    })

    return res.status(200).json({
        status: 'success',
        results: tasks.length,
        data: tasks,
    })
})

/**
 * Get a single task by ID
 */
export const getTaskById = catchAsync(async (req, res, next) => {
    const { id } = req.params

    // Return the newly created task with associations
    const task = await Task.findByPk(id, {
        include: [
            {
                model: Feature,
                attributes: ['id', 'feat_code', 'title'],
                include: [
                    {
                        model: Project,
                        attributes: ['id', 'name'],
                    },
                ],
            },
            {
                model: Status,
                as: 'taskStatus',
                attributes: ['id', 'short_code', 'label', 'colour'],
            },
            {
                model: User,
                as: 'taskAssignees',
                through: { attributes: [] },
                attributes: ['id', 'first_name', 'last_name'],
            },
            {
                model: User,
                as: 'creator',
                attributes: ['id', 'first_name', 'last_name', 'img_url'],
            },
            {
                model: Department,
                as: 'taskDepartment',
                attributes: ['id', 'short_code', 'label', 'colour', 'icon'],
            },
            {
                model: Priority,
                as: 'taskPriority',
                attributes: ['id', 'label', 'level', 'color'],
            },
            {
                model: Task,
                as: 'dependentTasks',
                through: { attributes: ['dependency_level'] },
                attributes: ['id', 'short_code', 'title', 'status_id'],
                include: [
                    {
                        model: Status,
                        as: 'taskStatus',
                        attributes: ['id', 'short_code', 'label'],
                    },
                    {
                        model: Department,
                        as: 'taskDepartment',
                        attributes: ['id', 'short_code', 'label', 'colour', 'icon'],
                    },
                    {
                        model: User,
                        as: 'taskAssignees',
                        through: { attributes: [] },
                        attributes: ['id', 'first_name', 'last_name', 'img_url'],
                    },
                ],
            },
            {
                model: Task,
                as: 'parentTasks',
                through: { attributes: ['dependency_level'] },
                attributes: ['id', 'short_code', 'title', 'status_id'],
                include: [
                    {
                        model: Status,
                        as: 'taskStatus',
                        attributes: ['id', 'short_code', 'label'],
                    },
                ],
            },
        ],
    })

    if (!task) {
        return next(new AppError('Task not found', 404))
    }

    return res.status(200).json({
        status: 'success',
        data: task,
    })
})

/**
 * Update a task
 */
export const updateTask = catchAsync(async (req, res, next) => {
    const { id } = req.params
    const {
        feat_id,
        short_code,
        title,
        description,
        time_estimate_hrs,
        start_date,
        due_date,
        status,
        assignee,
        department,
        bug,
        priority_id,
        parent_task_short_code,
        movedToIndex,
    } = req.body

    // Find the task
    const task = await Task.findByPk(id)
    if (!task) {
        return next(new AppError('Task not found', 404))
    }
    // Check if feature exists if feat_id is provided
    if (feat_id) {
        const feature = await Feature.findByPk(feat_id)
        if (!feature) {
            return next(new AppError('Feature not found', 404))
        }
    }

    // Check if status exists if status is provided
    if (status) {
        const statusRecord = await Status.findByPk(status)
        if (!statusRecord) {
            return next(new AppError('Status not found', 404))
        }
    }
    if (priority_id) {
        const priorityRecord = await Priority.findByPk(priority_id)
        if (!priorityRecord) {
            return next(new AppError('Priority not found', 404))
        }
    }

    // Check if assignees exist if provided
    if (assignee && assignee.length > 0) {
        const assigneeUsers = await User.findAll({
            where: {
                id: {
                    [Op.in]: assignee,
                },
            },
        })

        if (assigneeUsers.length !== assignee.length) {
            return next(new AppError('One or more assignees not found', 404))
        }
    }

    // Check if department exists if department is provided
    if (department) {
        const departmentRecord = await Department.findByPk(department)
        if (!departmentRecord) {
            return next(new AppError('Department not found', 404))
        }
    }

    // Check if parent task exists if provided
    let parentTask = null
    if (parent_task_short_code) {
        parentTask = await Task.findOne({
            where: { short_code: parent_task_short_code },
        })

        if (!parentTask) {
            return next(new AppError('Parent task not found', 404))
        }

        // Check that the parent task is not the same as the task being updated
        if (parentTask.id === parseInt(id)) {
            return next(new AppError('A task cannot be its own parent', 400))
        }
    }
    const transaction = await sequelize.transaction()
    try {
        // Handle kanban position updates if movedToIndex is provided or status changed
        if (movedToIndex !== undefined || (status !== undefined && status !== task.status_id)) {
            try {
                const currentPosition = task.kanban_position
                const currentStatusId = task.status_id
                const newStatusId = status !== undefined ? status : currentStatusId
                const isStatusChanged = newStatusId !== currentStatusId

                // If status is changing, reorder tasks in the old column
                if (isStatusChanged) {
                    // Get all tasks in the old status column (excluding current task)
                    const tasksInOldStatus = await Task.findAll({
                        where: {
                            status_id: currentStatusId,
                            id: { [Op.ne]: parseInt(id) },
                        },
                        order: [['kanban_position', 'ASC']],
                        transaction,
                    })

                    // Shift up all tasks below the current task in the old column
                    for (const otherTask of tasksInOldStatus) {
                        if (otherTask.kanban_position > currentPosition) {
                            await otherTask.update(
                                {
                                    kanban_position: otherTask.kanban_position - 1,
                                },
                                { transaction }
                            )
                        }
                    }

                    // Get all tasks in the new status column
                    const tasksInNewStatus = await Task.findAll({
                        where: {
                            status_id: newStatusId,
                        },
                        order: [['kanban_position', 'ASC']],
                        transaction,
                    })

                    // If movedToIndex is not specified, place at the end of the new column
                    if (movedToIndex === undefined) {
                        movedToIndex = tasksInNewStatus.length
                    }

                    // Shift down all tasks at or after the insertion point in the new column
                    for (const otherTask of tasksInNewStatus) {
                        if (otherTask.kanban_position >= movedToIndex) {
                            await otherTask.update(
                                {
                                    kanban_position: otherTask.kanban_position + 1,
                                },
                                { transaction }
                            )
                        }
                    }

                    // Set the task's new position
                    task.kanban_position = movedToIndex
                }
                // If staying in the same column but changing position
                else if (movedToIndex !== undefined) {
                    // Get all tasks in the same status column (excluding current task)
                    const tasksInSameStatus = await Task.findAll({
                        where: {
                            status_id: currentStatusId,
                            id: { [Op.ne]: parseInt(id) },
                        },
                        order: [['kanban_position', 'ASC']],
                        transaction,
                    })

                    // Moving down: Shift all tasks between old position and new position up by 1
                    if (currentPosition < movedToIndex) {
                        for (const otherTask of tasksInSameStatus) {
                            if (otherTask.kanban_position > currentPosition && otherTask.kanban_position <= movedToIndex) {
                                await otherTask.update(
                                    {
                                        kanban_position: otherTask.kanban_position - 1,
                                    },
                                    { transaction }
                                )
                            }
                        }
                    }
                    // Moving up: Shift all tasks between new position and old position down by 1
                    else if (currentPosition > movedToIndex) {
                        for (const otherTask of tasksInSameStatus) {
                            if (otherTask.kanban_position >= movedToIndex && otherTask.kanban_position < currentPosition) {
                                await otherTask.update(
                                    {
                                        kanban_position: otherTask.kanban_position + 1,
                                    },
                                    { transaction }
                                )
                            }
                        }
                    }

                    // Set the moved task's position to the target position
                    task.kanban_position = movedToIndex
                }
            } catch (error) {
                console.error('Error reordering tasks:', error)
                throw error
            }
        }
        // Update the task
        await task.update(
            {
                feat_id: feat_id !== undefined ? feat_id : task.feat_id,
                short_code: short_code !== undefined ? short_code : task.short_code,
                title: title !== undefined ? title : task.title,
                description: description !== undefined ? description : task.description,
                time_estimate_hrs: time_estimate_hrs !== undefined ? time_estimate_hrs : task.time_estimate_hrs,
                start_date: task.start_date || start_date !== undefined ? start_date : null,
                due_date: due_date !== undefined ? due_date : task.due_date,
                status_id: status !== undefined ? status : task.status_id,
                department: department !== undefined ? department : task.department,
                priority_id: priority_id !== undefined ? priority_id : task.priority_id,
                bug: bug !== undefined ? bug : task.bug,
                kanban_position: movedToIndex !== undefined ? movedToIndex : task.kanban_position,
            },
            { transaction }
        )

        // Update task-user mappings if assignees are provided
        if (assignee) {
            // Remove existing mappings
            await TaskUserMapping.destroy(
                {
                    where: { task_id: id },
                },
                { transaction }
            )

            // Create new mappings
            if (assignee.length > 0) {
                const taskUserMappings = assignee.map((userId) => ({
                    task_id: id,
                    user_id: userId,
                }))

                await TaskUserMapping.bulkCreate(taskUserMappings, { transaction })
            }
        }

        // Update task dependency if parent task is provided
        if (parent_task_short_code !== undefined) {
            // Remove existing dependencies where this task is dependent
            await TaskDependencyMapping.destroy({
                where: { dependant_task_id: id },
            })

            // Create new dependency if parent task is provided
            if (parentTask) {
                await TaskDependencyMapping.create({
                    task_id: parentTask.id,
                    dependant_task_id: id,
                    dependency_level: 1, // Default dependency level
                })
            }
        }
        await transaction.commit()
        // Fetch the updated task with associations
        const updatedTask = await Task.findByPk(id, {
            include: [
                {
                    model: Feature,
                    attributes: ['id', 'feat_code', 'title'],
                    include: [
                        {
                            model: Project,
                            attributes: ['id', 'name'],
                        },
                    ],
                },
                {
                    model: Status,
                    as: 'taskStatus',
                    attributes: ['id', 'short_code', 'label', 'colour'],
                },
                {
                    model: User,
                    as: 'taskAssignees',
                    through: { attributes: [] },
                    attributes: ['id', 'first_name', 'last_name'],
                },
                {
                    model: User,
                    as: 'creator',
                    attributes: ['id', 'first_name', 'last_name'],
                },
                {
                    model: Department,
                    as: 'taskDepartment',
                    attributes: ['id', 'short_code', 'label', 'colour', 'icon'],
                },
                {
                    model: Priority,
                    as: 'taskPriority',
                    attributes: ['id', 'label', 'level'],
                },
                {
                    model: Task,
                    as: 'parentTasks',
                    through: { attributes: ['dependency_level'] },
                    attributes: ['id', 'short_code', 'title'],
                    include: [
                        {
                            model: User,
                            as: 'taskAssignees',
                            through: { attributes: [] },
                            attributes: ['id', 'first_name', 'last_name'],
                        },
                        {
                            model: Status,
                            as: 'taskStatus',
                            attributes: ['id', 'short_code', 'label'],
                        },
                        {
                            model: Department,
                            as: 'taskDepartment',
                            attributes: ['id', 'short_code', 'label', 'colour', 'icon'],
                        },
                    ],
                },
                {
                    model: Task,
                    as: 'dependentTasks',
                    through: { attributes: ['dependency_level'] },
                    attributes: ['id', 'short_code', 'title'],
                    include: [
                        {
                            model: User,
                            as: 'taskAssignees',
                            through: { attributes: [] },
                            attributes: ['id', 'first_name', 'last_name'],
                        },
                        {
                            model: Status,
                            as: 'taskStatus',
                            attributes: ['id', 'short_code', 'label'],
                        },
                        {
                            model: Department,
                            as: 'taskDepartment',
                            attributes: ['id', 'short_code', 'label', 'colour', 'icon'],
                        },
                    ],
                },
            ],
        })

        // Update vector (non-blocking)
        vectorManager.safeExecute(
            () =>
                vectorManager.upsertVector('task', {
                    id: updatedTask.id,
                    short_code: updatedTask.short_code,
                    title: updatedTask.title,
                    description: updatedTask.description,
                    feat_id: updatedTask.feat_id,
                    project_id: updatedTask.feature?.project_id,
                    status_id: updatedTask.status_id,
                    assignee: updatedTask.assignee,
                    department: updatedTask.department,
                    priority_id: updatedTask.priority_id,
                    time_estimate_hrs: updatedTask.time_estimate_hrs,
                    start_date: updatedTask.start_date,
                    due_date: updatedTask.due_date,
                    created_by: updatedTask.created_by,
                    feature: updatedTask.feature,
                    project: updatedTask.feature?.project,
                    assignedUser: updatedTask.taskAssignees,
                    status: updatedTask.taskStatus,
                    department: updatedTask.taskDepartment,
                    priority: updatedTask.taskPriority,
                    creator: updatedTask.creator,
                    parentTasks: updatedTask.parentTasks,
                    dependentTasks: updatedTask.dependentTasks,
                }),
            'create task vector'
        )
        return res.status(200).json({
            status: 'success',
            data: updatedTask,
        })
    } catch (error) {
        await transaction.rollback()
        return next(new AppError(`Failed to update task: ${error.message}`, 500))
    }
})

/**
 * Delete a task
 */
export const deleteTask = catchAsync(async (req, res, next) => {
    const { id } = req.params

    const task = await Task.findByPk(id)
    if (!task) {
        return next(new AppError('Task not found', 404))
    }

    // Soft delete the task (using paranoid option)
    await task.destroy()

    return res.status(204).json({
        status: 'success',
        data: null,
    })
})
