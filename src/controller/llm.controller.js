import { generateAITasks } from '../llm/agents/conversation.agent.js'
import routeMainAgent from '../llm/main.agent.js'
import { getSession, updateSession } from '../llm/utils/sessionState.utils.js'
import { catchAsync } from '../utils/catch-async.js'
import { AppError } from '../utils/app-error.js'

/**
 * AI router endpoint using LangGraph
 * Message gets routed intelligently with memory (main.agent.js)
 */
export const aiRouterController = catchAsync(async (req, res, next) => {
    res.setHeader('Content-Type', 'text/event-stream')
    res.setHeader('Cache-Control', 'no-cache')
    res.setHeader('Connection', 'keep-alive')
    res.setHeader('X-Accel-Buffering', 'no')
    res.setHeader('Access-Control-Allow-Origin', '*')
    res.setHeader('Access-Control-Allow-Headers', '*')

    const { message, sessionId, generateTasks: shouldGenerateTasks, selectedProjectId } = req.body

    if (!message || !sessionId) {
        throw new AppError('Message and sessionId are required', 400)
    }

    if (shouldGenerateTasks) {
        await generateAITasks(sessionId, res)
        return
    }

    const session = getSession(sessionId)
    const lastIntent = session.lastIntent || null

    const updatedIntent = await routeMainAgent({
        message,
        sessionId,
        res,
        lastIntent,
        selectedProjectId,
    })

    updateSession(sessionId, { lastIntent: updatedIntent })
})
