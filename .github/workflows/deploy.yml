name: Deploy Backend to GHCR and VPS

on:
    push:
        branches:
            - main
    workflow_dispatch:

jobs:
    deploy:
        runs-on: ubuntu-latest
        env:
            SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

        steps:
            - name: Notify In Slack When Starting
              uses: act10ns/slack@v2
              with:
                  status: starting
                  config: .github/slack.yml
                  channel: '#deployments'
              if: always()

            - name: Checkout Code
              uses: actions/checkout@v3

            - name: Log in to GitHub Container Registry
              uses: docker/login-action@v2
              with:
                  registry: ghcr.io
                  username: ${{ github.actor }}
                  password: ${{ secrets.IMAGE_PUSH_TOKEN }}

            - name: Build and Push Backend Image
              run: |
                  docker build -t ghcr.io/beurokrat/fun2plan-backend:latest .
                  docker push ghcr.io/beurokrat/fun2plan-backend:latest

            - name: Deploy Backend to VPS via SSH
              uses: appleboy/ssh-action@v0.1.6
              with:
                  host: ${{ secrets.VPS_HOST }}
                  username: ${{ secrets.VPS_USER }}
                  key: ${{ secrets.VPS_PRIVATE_KEY }}
                  script: |
                      set -ex
                      docker pull ghcr.io/beurokrat/fun2plan-backend:latest || true
                      docker-compose down && docker-compose up -d --build

            - name: Notify In Slack After Completion
              uses: act10ns/slack@v2
              with:
                  status: ${{ job.status }}
                  config: .github/slack.yml
                  channel: '#deployments'
              if: always()
