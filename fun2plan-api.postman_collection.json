{"info": {"_postman_id": "f8a5e9c3-7b2d-4e5f-8d9a-1c2b3d4e5f6a", "name": "Fun2Plan API", "description": "API collection for Fun2Plan backend services", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON>", "description": "Authentication endpoints", "item": [{"name": "Signup", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"<PERSON><PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"Password123\",\n    \"confirmPassword\": \"Password123\",\n    \"userType\": \"1\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/signup", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "signup"]}, "description": "Register a new user"}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"Password123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "login"]}, "description": "Login with email and password"}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"refreshToken\": \"your-refresh-token-here\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/refresh", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "refresh"]}, "description": "Refresh access token using refresh token"}}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"code\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/verify-email", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "verify-email"]}, "description": "Verify user email with verification code"}}, {"name": "Resend Verification Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/resend-verification", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "resend-verification"]}, "description": "Resend verification code to user email"}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/logout", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "logout"]}, "description": "Logout and invalidate refresh token"}}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/forgot-password", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "forgot-password"]}, "description": "Request a password reset code"}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"token\": \"123456\",\n    \"password\": \"NewPassword123\",\n    \"confirmPassword\": \"NewPassword123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/auth/reset-password", "host": ["{{baseUrl}}"], "path": ["api", "v1", "auth", "reset-password"]}, "description": "Reset password using token"}}]}, {"name": "User", "description": "User management endpoints", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/user", "host": ["{{baseUrl}}"], "path": ["api", "v1", "user"]}, "description": "Get all users (admin only)"}}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/user/1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "user", "1"]}, "description": "Get user by ID (admin only)"}}, {"name": "Create User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"Password123\",\n    \"confirmPassword\": \"Password123\",\n    \"userType\": \"1\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/user", "host": ["{{baseUrl}}"], "path": ["api", "v1", "user"]}, "description": "Create a new user (admin only)"}}, {"name": "Update User", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"<PERSON><PERSON>\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/user/1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "user", "1"]}, "description": "Update user by ID (admin only)"}}, {"name": "Delete User", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/user/1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "user", "1"]}, "description": "Delete user by ID (admin only)"}}]}, {"name": "Workspace", "description": "Workspace management endpoints", "item": [{"name": "Create Workspace", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"My Workspace\",\n    \"img_url\": \"https://example.com/workspace-image.jpg\",\n    \"plan\": 0\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/workspace", "host": ["{{baseUrl}}"], "path": ["api", "v1", "workspace"]}, "description": "Create a new workspace"}}, {"name": "Get User Workspaces", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/workspace", "host": ["{{baseUrl}}"], "path": ["api", "v1", "workspace"]}, "description": "Get all workspaces for the current user"}}, {"name": "Get Workspace by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/workspace/1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "workspace", "1"]}, "description": "Get workspace by ID"}}, {"name": "Update Workspace", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Workspace Name\",\n    \"img_url\": \"https://example.com/updated-image.jpg\",\n    \"plan\": 1\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/workspace/1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "workspace", "1"]}, "description": "Update workspace by ID"}}, {"name": "Delete Workspace", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/workspace/1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "workspace", "1"]}, "description": "Delete workspace by ID"}}, {"name": "Add User to Workspace", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"workspace_id\": 1,\n    \"user_id\": 2\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/workspace/user", "host": ["{{baseUrl}}"], "path": ["api", "v1", "workspace", "user"]}, "description": "Add a user to a workspace"}}, {"name": "Remove User from Workspace", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/workspace/1/user/2", "host": ["{{baseUrl}}"], "path": ["api", "v1", "workspace", "1", "user", "2"]}, "description": "Remove a user from a workspace"}}]}, {"name": "Project", "description": "Project management endpoints", "item": [{"name": "Create Project", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"My Project\",\n    \"overview\": \"This is a sample project description\",\n    \"workspace_id\": 1,\n    \"status_id\": 1\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/project", "host": ["{{baseUrl}}"], "path": ["api", "v1", "project"]}, "description": "Create a new project"}}, {"name": "Get All Projects", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/project?workspace_id=1&status_id=1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "project"], "query": [{"key": "workspace_id", "value": "1", "description": "Filter by workspace ID"}, {"key": "status_id", "value": "1", "description": "Filter by status ID"}]}, "description": "Get all projects with optional filtering"}}, {"name": "Get Project by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/project/1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "project", "1"]}, "description": "Get project by ID"}}, {"name": "Update Project", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Project Name\",\n    \"overview\": \"Updated project description\",\n    \"status_id\": 2\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/project/1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "project", "1"]}, "description": "Update project by ID"}}, {"name": "Delete Project", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/project/1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "project", "1"]}, "description": "Delete project by ID"}}, {"name": "Add User to Project", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"project_id\": 1,\n    \"user_id\": 2\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/project/user", "host": ["{{baseUrl}}"], "path": ["api", "v1", "project", "user"]}, "description": "Add a user to a project"}}, {"name": "Remove User from Project", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/project/1/user/2", "host": ["{{baseUrl}}"], "path": ["api", "v1", "project", "1", "user", "2"]}, "description": "Remove a user from a project"}}]}, {"name": "Feature", "description": "Feature management endpoints", "item": [{"name": "Create Feature", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"feat_code\": \"FEAT-001\",\n    \"title\": \"User Authentication\",\n    \"description\": \"Implement user authentication with JWT\",\n    \"draft\": true,\n    \"project_id\": 1,\n    \"status_id\": 1\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/feature", "host": ["{{baseUrl}}"], "path": ["api", "v1", "feature"]}, "description": "Create a new feature"}}, {"name": "Get All Features", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/feature?project_id=1&status_id=1&draft=true", "host": ["{{baseUrl}}"], "path": ["api", "v1", "feature"], "query": [{"key": "project_id", "value": "1", "description": "Filter by project ID"}, {"key": "status_id", "value": "1", "description": "Filter by status ID"}, {"key": "draft", "value": "true", "description": "Filter by draft status"}]}, "description": "Get all features with optional filtering"}}, {"name": "Get Feature by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/feature/1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "feature", "1"]}, "description": "Get feature by ID"}}, {"name": "Update Feature", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Updated Feature Title\",\n    \"description\": \"Updated feature description\",\n    \"draft\": false,\n    \"status_id\": 2\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/feature/1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "feature", "1"]}, "description": "Update feature by ID"}}, {"name": "Delete Feature", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/feature/1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "feature", "1"]}, "description": "Delete feature by ID"}}]}, {"name": "Task", "description": "Task management endpoints", "item": [{"name": "Create Task", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"feat_id\": 1,\n    \"short_code\": \"TASK-001\",\n    \"title\": \"Implement JWT Authentication\",\n    \"description\": \"Create JWT authentication middleware\",\n    \"time_estimate_hrs\": 4,\n    \"start_date\": \"2023-06-01T00:00:00.000Z\",\n    \"due_date\": \"2023-06-07T00:00:00.000Z\",\n    \"status\": 1,\n    \"assignee\": 1\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/task", "host": ["{{baseUrl}}"], "path": ["api", "v1", "task"]}, "description": "Create a new task"}}, {"name": "Get All Tasks", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/task?feat_id=1&status=1&assignee=1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "task"], "query": [{"key": "feat_id", "value": "1", "description": "Filter by feature ID"}, {"key": "status", "value": "1", "description": "Filter by status ID"}, {"key": "assignee", "value": "1", "description": "Filter by assignee ID"}]}, "description": "Get all tasks with optional filtering"}}, {"name": "Get Task by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/task/1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "task", "1"]}, "description": "Get task by ID"}}, {"name": "Update Task", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Updated Task Title\",\n    \"description\": \"Updated task description\",\n    \"time_estimate_hrs\": 6,\n    \"status\": 2,\n    \"assignee\": 2\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/task/1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "task", "1"]}, "description": "Update task by ID"}}, {"name": "Delete Task", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/v1/task/1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "task", "1"]}, "description": "Delete task by ID"}}]}, {"name": "LLM", "description": "LLM integration endpoints", "item": [{"name": "Generate Tasks", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"feature\": \"User Authentication\",\n    \"requirement\": \"Implement secure user authentication with email/password login, password reset, and two-factor authentication.\",\n    \"techStack\": [\"Node.js\", \"Express\", \"JWT\", \"MongoDB\"]\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/llm", "host": ["{{baseUrl}}"], "path": ["api", "v1", "llm"]}, "description": "Generate tasks using LLM"}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3001", "type": "string"}, {"key": "accessToken", "value": "your-access-token-here", "type": "string"}]}