{"name": "fun2plan-backend", "version": "0.0.1", "description": "AI and export service", "main": "src/server.js", "type": "module", "scripts": {"start:dev": "nodemon app.js", "db:migrate": "npx sequelize-cli db:migrate", "db:migrate:undo": "npx sequelize-cli db:migrate:undo", "db:migrate:undo:all": "npx sequelize-cli db:migrate:undo:all", "db:seed:all": "npx sequelize-cli db:seed:all", "db:seed:undo:all": "npx sequelize-cli db:seed:undo:all", "db:reset:full": "npx sequelize-cli db:migrate:undo:all && npx sequelize-cli db:migrate && npx sequelize-cli db:seed:undo:all && npx sequelize-cli db:seed:all"}, "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@langchain/community": "^0.3.42", "@langchain/core": "^0.3.55", "@langchain/langgraph": "^0.2.72", "@langchain/openai": "^0.2.6", "@qdrant/js-client-rest": "^1.14.0", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "cheerio": "^1.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.5", "express": "^4.19.2", "jsonwebtoken": "^9.0.2", "langchain": "^0.2.13", "nodemailer": "^6.10.1", "openai": "^4.96.2", "pg": "^8.12.0", "pg-hstore": "^2.3.4", "sequelize": "^6.37.3", "socket.io": "^4.7.5", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"nodemon": "^3.1.4", "sequelize-cli": "^6.6.2"}}