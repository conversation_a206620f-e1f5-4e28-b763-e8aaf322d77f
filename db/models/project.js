import { DataTypes } from 'sequelize'
import sequelize from '../../config/db.config.js'

const Project = sequelize.define(
    'projects',
    {
        id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: DataTypes.INTEGER,
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notNull: {
                    msg: 'name cannot be null',
                },
                notEmpty: {
                    msg: 'name cannot be empty',
                },
            },
        },
        overview: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        workspace_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        status_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        stack: {
            type: DataTypes.JSONB, // Store stack as array
            allowNull: false,
        },
    },
    {
        sequelize,
        modelName: 'Project',
        tableName: 'projects',
        timestamps: true, // adds created_at and updated_at
        paranoid: true, // adds deleted_at (for soft deletes)
        underscored: true, // uses snake_case column names
    },
)

// Define associations after all models are imported
const setupAssociations = async () => {
    try {
        const User = (await import('./user.js')).default
        const Workspace = (await import('./workspace.js')).default
        const Status = (await import('./status.js')).default
        const UserProjectMapping = (await import('./user-project-mapping.js')).default
        const Feature = (await import('./feature.js')).default

        Project.belongsTo(User, { foreignKey: 'created_by', as: 'creator' })
        Project.belongsTo(Workspace, { foreignKey: 'workspace_id' })
        Project.belongsTo(Status, { foreignKey: 'status_id' })
        Project.hasMany(Feature, { foreignKey: 'project_id' })
        Project.belongsToMany(User, {
            through: UserProjectMapping,
            foreignKey: 'project_id',
            otherKey: 'user_id',
        })
    } catch (error) {
        console.error('Error setting up Project associations:', error)
    }
}

// Setup associations asynchronously to avoid circular dependencies
setupAssociations()

export default Project
