import { DataTypes } from 'sequelize'
import sequelize from '../../config/db.config.js'

const Department = sequelize.define(
    'departments',
    {
        id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: DataTypes.INTEGER,
        },
        short_code: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notNull: {
                    msg: 'short_code cannot be null',
                },
                notEmpty: {
                    msg: 'short_code cannot be empty',
                },
            },
        },
        label: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notNull: {
                    msg: 'label cannot be null',
                },
                notEmpty: {
                    msg: 'label cannot be empty',
                },
            },
        },
        colour: {
            type: DataTypes.JSONB,
            allowNull: true,
        },
        icon: {
            type: DataTypes.STRING,
            allowNull: true,
        },
    },
    {
        sequelize,
        modelName: 'Department',
        tableName: 'departments',
        timestamps: true,
        paranoid: true,
        underscored: true,
    },
)

// Define associations after all models are imported
const setupAssociations = async () => {
    try {
        const Task = (await import('./task.js')).default

        Department.hasMany(Task, {
            foreignKey: 'department',
            as: 'tasks',
        })
    } catch (error) {
        console.error('Error setting up Department associations:', error)
    }
}

// Setup associations asynchronously to avoid circular dependencies
setupAssociations()

export default Department
