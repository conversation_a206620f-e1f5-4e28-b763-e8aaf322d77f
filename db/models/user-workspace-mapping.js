import { DataTypes } from 'sequelize'
import sequelize from '../../config/db.config.js'

const UserWorkspaceMapping = sequelize.define(
    'user_workspace_mapping',
    {
        id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: DataTypes.INTEGER,
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id',
            },
        },
        workspace_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'workspaces',
                key: 'id',
            },
        },
    },
    {
        sequelize,
        modelName: 'UserWorkspaceMapping',
        tableName: 'user_workspace_mapping',
        timestamps: true,
        paranoid: true,
        underscored: true,
        indexes: [
            {
                unique: true,
                fields: ['user_id', 'workspace_id'],
                name: 'unique_user_workspace_mapping',
                where: {
                    deleted_at: null,
                },
            },
        ],
    },
)

const setupAssociations = async () => {
    try {
        const User = (await import('./user.js')).default
        const Workspace = (await import('./workspace.js')).default

        UserWorkspaceMapping.belongsTo(User, { foreignKey: 'user_id' })
        UserWorkspaceMapping.belongsTo(Workspace, { foreignKey: 'workspace_id' })
    } catch (error) {
        console.error('Error setting up UserWorkspaceMapping associations:', error)
    }
}

setupAssociations()

export default UserWorkspaceMapping
