import { DataTypes } from 'sequelize'
import sequelize from '../../config/db.config.js'

const Timezone = sequelize.define(
    'timezones',
    {
        id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: DataTypes.INTEGER,
        },
        timezone_name: {
            type: DataTypes.STRING(50),
            allowNull: false,
            unique: true,
            validate: {
                notNull: {
                    msg: 'timezone_name cannot be null',
                },
                notEmpty: {
                    msg: 'timezone_name cannot be empty',
                },
            },
        },
        display_name: {
            type: DataTypes.STRING(200),
            allowNull: false,
            validate: {
                notNull: {
                    msg: 'display_name cannot be null',
                },
                notEmpty: {
                    msg: 'display_name cannot be empty',
                },
            },
        },
        city: {
            type: DataTypes.STRING(100),
            allowNull: false,
            validate: {
                notNull: {
                    msg: 'city cannot be null',
                },
                notEmpty: {
                    msg: 'city cannot be empty',
                },
            },
        },
        country_name: {
            type: DataTypes.STRING(100),
            allowNull: false,
            validate: {
                notNull: {
                    msg: 'country_name cannot be null',
                },
                notEmpty: {
                    msg: 'country_name cannot be empty',
                },
            },
        },
        country_code: {
            type: DataTypes.STRING(2),
            allowNull: false,
            validate: {
                notNull: {
                    msg: 'country_code cannot be null',
                },
                notEmpty: {
                    msg: 'country_code cannot be empty',
                },
            },
        },
        gmt_offset: {
            type: DataTypes.STRING(10),
            allowNull: false,
            validate: {
                notNull: {
                    msg: 'gmt_offset cannot be null',
                },
                notEmpty: {
                    msg: 'gmt_offset cannot be empty',
                },
            },
        },
        dst_offset: {
            type: DataTypes.STRING(10),
            allowNull: true,
        },
        is_dst_active: {
            type: DataTypes.BOOLEAN,
            defaultValue: false,
        },
        created_at: {
            allowNull: false,
            type: DataTypes.DATE,
        },
        updated_at: {
            allowNull: false,
            type: DataTypes.DATE,
        },
    },
    {
        sequelize,
        modelName: 'Timezone',
        tableName: 'timezones',
        underscored: true,
        timestamps: true,
    }
)

// Define associations after all models are imported
const setupAssociations = async () => {
    try {
        const User = (await import('./user.js')).default

        Timezone.hasMany(User, { foreignKey: 'timezone_id' })
    } catch (error) {
        console.error('Error setting up Timezone associations:', error)
    }
}

// Setup associations asynchronously to avoid circular dependencies
setupAssociations()

export default Timezone
