import { DataTypes } from 'sequelize'
import sequelize from '../../config/db.config.js'

const Feature = sequelize.define(
    'features',
    {
        id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: DataTypes.INTEGER,
        },
        feat_code: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notNull: {
                    msg: 'feat_code cannot be null',
                },
                notEmpty: {
                    msg: 'feat_code cannot be empty',
                },
            },
        },
        title: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notNull: {
                    msg: 'title cannot be null',
                },
                notEmpty: {
                    msg: 'title cannot be empty',
                },
            },
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        draft: {
            type: DataTypes.BOOLEAN,
            defaultValue: true,
        },
        project_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'projects',
                key: 'id',
            },
        },
        status_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'statuses',
                key: 'id',
            },
        },
        created_by: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id',
            },
        },
    },
    {
        sequelize,
        modelName: 'Feature',
        tableName: 'features',
        timestamps: true, // adds created_at and updated_at
        paranoid: true, // adds deleted_at (for soft deletes)
        underscored: true, // uses snake_case column names
    },
)

// Define associations after all models are imported
const setupAssociations = async () => {
    try {
        const Project = (await import('./project.js')).default
        const Status = (await import('./status.js')).default
        const User = (await import('./user.js')).default
        const Task = (await import('./task.js')).default

        Feature.belongsTo(Project, { foreignKey: 'project_id' })
        Feature.belongsTo(Status, { foreignKey: 'status_id' })
        Feature.belongsTo(User, { foreignKey: 'created_by', as: 'creator' })
        Feature.hasMany(Task, { foreignKey: 'feat_id' })
    } catch (error) {
        console.error('Error setting up Feature associations:', error)
    }
}

// Setup associations asynchronously to avoid circular dependencies
setupAssociations()

export default Feature
