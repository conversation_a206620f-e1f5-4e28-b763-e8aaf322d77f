import { DataTypes } from 'sequelize'
import sequelize from '../../config/db.config.js'

const Invitation = sequelize.define(
    'invitations',
    {
        id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: DataTypes.INTEGER,
        },
        workspace_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'workspaces',
                key: 'id',
            },
            onUpdate: 'CASCADE',
            onDelete: 'CASCADE',
        },
        email: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        invite_token: {
            type: DataTypes.STRING,
            allowNull: false,
            unique: true,
        },
        expires_at: {
            type: DataTypes.DATE,
            allowNull: false,
        },
        status: {
            type: DataTypes.ENUM('pending', 'accepted', 'expired'),
            allowNull: false,
            defaultValue: 'pending',
        },
        invited_by: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id',
            },
            onUpdate: 'CASCADE',
        },
    },
    {
        sequelize,
        modelName: 'Invitation',
        tableName: 'invitations',
        timestamps: true,
        paranoid: true,
        underscored: true,
    }
)

const setupAssociations = async () => {
    try {
        const Workspace = (await import('./workspace.js')).default
        const User = (await import('./user.js')).default

        Invitation.belongsTo(Workspace, { foreignKey: 'workspace_id' })
        Invitation.belongsTo(User, { foreignKey: 'invited_by', as: 'inviter' })
    } catch (error) {
        console.error('Error setting up Invitation associations:', error)
    }
}

setupAssociations()

export default Invitation
