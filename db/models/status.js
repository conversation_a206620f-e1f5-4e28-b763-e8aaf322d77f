import { DataTypes } from 'sequelize'
import sequelize from '../../config/db.config.js'

const Status = sequelize.define(
    'statuses',
    {
        id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: DataTypes.INTEGER,
        },
        short_code: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notNull: {
                    msg: 'short_code cannot be null',
                },
                notEmpty: {
                    msg: 'short_code cannot be empty',
                },
            },
        },
        label: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notNull: {
                    msg: 'label cannot be null',
                },
                notEmpty: {
                    msg: 'label cannot be empty',
                },
            },
        },
        kanban_column: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
        },
        column_order: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        colour: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notNull: {
                    msg: 'colour cannot be null',
                },
                notEmpty: {
                    msg: 'colour cannot be empty',
                },
            },
        },
    },
    {
        sequelize,
        modelName: 'Status',
        tableName: 'statuses',
        timestamps: true, // adds created_at and updated_at
        paranoid: true, // adds deleted_at (for soft deletes)
        underscored: true, // uses snake_case column names
    },
)

// Define associations after all models are imported
const setupAssociations = async () => {
    try {
        const Project = (await import('./project.js')).default
        const Feature = (await import('./feature.js')).default
        const Task = (await import('./task.js')).default

        Status.hasMany(Project, { foreignKey: 'status_id' })
        Status.hasMany(Feature, { foreignKey: 'status_id' })
        Status.hasMany(Task, { foreignKey: 'status_id', as: 'tasks' })
    } catch (error) {
        console.error('Error setting up Status associations:', error)
    }
}

// Setup associations asynchronously to avoid circular dependencies
setupAssociations()

export default Status
