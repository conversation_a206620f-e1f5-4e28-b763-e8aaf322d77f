'use strict'
import { DataTypes } from 'sequelize'
import sequelize from '../../config/db.config.js'
import { AppError } from '../../src/utils/app-error.js'
import { passwordUtils } from '../../src/utils/password.util.js'
import TaskUserMapping from './task-user-mapping.js'

const User = sequelize.define(
    'users',
    {
        id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: DataTypes.INTEGER,
        },
        user_type: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'user_roles',
                key: 'id',
            },
            onUpdate: 'CASCADE',
            validate: {
                notNull: {
                    msg: 'firstName cannot be null',
                },
                notEmpty: {
                    msg: 'firstName cannot be empty',
                },
            },
        },
        role: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'team_member_roles',
                key: 'id',
            },
            onUpdate: 'CASCADE',
        },
        first_name: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notNull: {
                    msg: 'firstName cannot be null',
                },
                notEmpty: {
                    msg: 'firstName cannot be empty',
                },
            },
        },
        last_name: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notNull: {
                    msg: 'lastName cannot be null',
                },
                notEmpty: {
                    msg: 'lastName cannot be empty',
                },
            },
        },
        email: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notNull: {
                    msg: 'email cannot be null',
                },
                notEmpty: {
                    msg: 'email cannot be empty',
                },
                isEmail: {
                    msg: 'Invalid email id',
                },
            },
        },
        password: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notNull: {
                    msg: 'password cannot be null',
                },
                notEmpty: {
                    msg: 'password cannot be empty',
                },
            },
        },
        confirm_password: {
            type: DataTypes.VIRTUAL,
            set(value) {
                if (this.password.length < 7) {
                    throw new AppError('Password length must be grater than 7', 400)
                }
                if (value !== this.password) {
                    throw new AppError('Password and confirm password must be the same', 400)
                }
                // Password will be hashed in the beforeCreate/beforeUpdate hooks
            },
        },
        bio: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        timezone_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'timezones',
                key: 'id',
            },
            onUpdate: 'CASCADE',
            onDelete: 'SET NULL',
        },
        cover_image_url: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        img_url: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        verification_code: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        verification_code_expires_at: {
            type: DataTypes.DATE,
            allowNull: true,
        },
        is_verified: {
            type: DataTypes.BOOLEAN,
            defaultValue: false,
        },
        refresh_token: {
            allowNull: true,
            type: DataTypes.STRING,
        },
        // Timestamps (created_at, updated_at) are managed automatically by Sequelize
        // deleted_at is managed by the paranoid option
    },
    {
        underscored: true,
        timestamps: true,
        paranoid: true,
        freezeTableName: true,
        defaultScope: {
            attributes: { exclude: ['password', 'refresh_token', 'verification_code'] },
        },
        scopes: {
            withPassword: {
                attributes: { include: ['password'] },
            },
        },
        hooks: {
            beforeValidate: async (user) => {
                if (user.password) {
                    passwordUtils.validatePassword(user.password)
                }
            },
            beforeCreate: async (user) => {
                if (user.password) {
                    user.password = await passwordUtils.hashPassword(user.password)
                }
            },
            beforeUpdate: async (user) => {
                if (user.changed('password')) {
                    passwordUtils.validatePassword(user.password)
                    user.password = await passwordUtils.hashPassword(user.password)
                }
            },
        },
    }
)

// Define associations after all models are imported
const setupAssociations = async () => {
    try {
        const Project = (await import('./project.js')).default
        const Feature = (await import('./feature.js')).default
        const Task = (await import('./task.js')).default
        const Workspace = (await import('./workspace.js')).default
        const UserWorkspaceMapping = (await import('./user-workspace-mapping.js')).default
        const UserProjectMapping = (await import('./user-project-mapping.js')).default
        const TeamMemberRole = (await import('./team-member-role.js')).default
        const Timezone = (await import('./timezones.js')).default

        User.hasMany(Project, { foreignKey: 'created_by', as: 'createdProjects' })
        User.hasMany(Feature, { foreignKey: 'created_by', as: 'createdFeatures' })
        User.hasMany(Task, { foreignKey: 'created_by', as: 'createdTasks' })
        User.hasMany(Task, { foreignKey: 'assignee', as: 'assignedTasks' })
        User.belongsToMany(Workspace, {
            through: UserWorkspaceMapping,
            foreignKey: 'user_id',
            otherKey: 'workspace_id',
        })
        User.belongsToMany(Project, {
            through: UserProjectMapping,
            foreignKey: 'user_id',
            otherKey: 'project_id',
        })
        User.belongsToMany(Task, {
            foreignKey: 'user_id',
            as: 'taskAssignees',
            through: TaskUserMapping,
        })
        User.belongsTo(TeamMemberRole, { foreignKey: 'role' })
        User.belongsTo(Timezone, { foreignKey: 'timezone_id' })
    } catch (error) {
        console.error('Error setting up User associations:', error)
    }
}

// Setup associations asynchronously to avoid circular dependencies
setupAssociations()

export default User // Exporting the User model
