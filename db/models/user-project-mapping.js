import { DataTypes } from 'sequelize'
import sequelize from '../../config/db.config.js'

const UserProjectMapping = sequelize.define(
    'user_project_mapping',
    {
        id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: DataTypes.INTEGER,
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id',
            },
        },
        project_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'projects',
                key: 'id',
            },
        },
    },
    {
        sequelize,
        modelName: 'UserProjectMapping',
        tableName: 'user_project_mapping',
        timestamps: true,
        paranoid: true,
        underscored: true,
        indexes: [
            {
                unique: true,
                fields: ['user_id', 'project_id'],
                name: 'unique_user_project_mapping',
                where: {
                    deleted_at: null,
                },
            },
        ],
    }
)

const setupAssociations = async () => {
    try {
        const User = (await import('./user.js')).default
        const Project = (await import('./project.js')).default

        UserProjectMapping.belongsTo(User, { foreignKey: 'user_id' })
        UserProjectMapping.belongsTo(Project, { foreignKey: 'project_id' })
    } catch (error) {
        console.error('Error setting up UserProjectMapping associations:', error)
    }
}

setupAssociations()

export default UserProjectMapping
