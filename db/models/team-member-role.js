import { DataTypes } from 'sequelize'
import sequelize from '../../config/db.config.js'
import User from './user.js'

const TeamMemberRole = sequelize.define(
    'team_member_roles',
    {
        id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: DataTypes.INTEGER,
        },
        role: {
            type: DataTypes.STRING,
            allowNull: false,
            unique: true,
            validate: {
                notNull: {
                    msg: 'role cannot be null',
                },
                notEmpty: {
                    msg: 'role cannot be empty',
                },
            },
        },
        created_at: {
            allowNull: false,
            type: DataTypes.DATE,
        },
        updated_at: {
            allowNull: false,
            type: DataTypes.DATE,
        },
    },
    {
        sequelize,
        modelName: 'TeamMemberRole',
        tableName: 'team_member_roles',
        underscored: true,
        timestamps: true,
    }
)

// Define the association with User model
TeamMemberRole.hasMany(User, { foreignKey: 'role' })
User.belongsTo(TeamMemberRole, { foreignKey: 'role' })

export default TeamMemberRole
