import { DataTypes } from 'sequelize'
import sequelize from '../../config/db.config.js'

const TaskUserMapping = sequelize.define(
    'task_user_mapping',
    {
        id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: DataTypes.INTEGER,
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id',
            },
        },
        task_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'tasks',
                key: 'id',
            },
        },
    },
    {
        sequelize,
        modelName: 'TaskUserMapping',
        tableName: 'task_user_mapping',
        timestamps: true,
        paranoid: true,
        underscored: true,
        indexes: [
            {
                unique: true,
                fields: ['user_id', 'task_id'],
                name: 'unique_task_user_mapping',
                where: {
                    deleted_at: null,
                },
            },
        ],
    }
)

const setupAssociations = async () => {
    try {
        const User = (await import('./user.js')).default
        const Task = (await import('./task.js')).default

        TaskUserMapping.belongsTo(User, { foreignKey: 'user_id', as: 'assignedUser' })
        TaskUserMapping.belongsTo(Task, { foreignKey: 'task_id', as: 'taskAssigned' })
    } catch (error) {
        console.error('Error setting up TaskUserMapping associations:', error)
    }
}

setupAssociations()

export default TaskUserMapping
