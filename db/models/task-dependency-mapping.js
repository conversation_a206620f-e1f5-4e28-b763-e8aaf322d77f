import { DataTypes } from 'sequelize'
import sequelize from '../../config/db.config.js'

const TaskDependencyMapping = sequelize.define(
    'task_dependency_mapping',
    {
        id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: DataTypes.INTEGER,
        },
        task_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'tasks',
                key: 'id',
            },
        },
        dependant_task_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'tasks',
                key: 'id',
            },
        },
        dependency_level: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
    },
    {
        sequelize,
        modelName: 'TaskDependencyMapping',
        tableName: 'task_dependency_mapping',
        timestamps: true,
        paranoid: true,
        underscored: true,
        indexes: [
            {
                unique: true,
                fields: ['task_id', 'dependant_task_id'],
                name: 'unique_task_dependency_mapping',
            },
        ],
    },
)

const setupAssociations = async () => {
    try {
        const Task = (await import('./task.js')).default

        TaskDependencyMapping.belongsTo(Task, {
            foreignKey: 'task_id',
            as: 'parentTask',
        })
        TaskDependencyMapping.belongsTo(Task, {
            foreignKey: 'dependant_task_id',
            as: 'dependantTask',
        })
    } catch (error) {
        console.error('Error setting up TaskDependencyMapping associations:', error)
    }
}

setupAssociations()

export default TaskDependencyMapping
