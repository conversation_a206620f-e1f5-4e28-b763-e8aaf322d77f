'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up(queryInterface, Sequelize) {
        await queryInterface.createTable(
            'statuses',
            {
                id: {
                    allowNull: false,
                    autoIncrement: true,
                    primaryKey: true,
                    type: Sequelize.INTEGER,
                },
                short_code: {
                    type: Sequelize.STRING,
                    allowNull: false,
                },
                label: {
                    type: Sequelize.STRING,
                    allowNull: false,
                },
                colour: {
                    type: Sequelize.STRING,
                    allowNull: false,
                },
                kanban_column: {
                    type: Sequelize.BOOLEAN,
                    allowNull: true,
                    defaultValue: false,
                },
                column_order: {
                    type: Sequelize.INTEGER,
                    allowNull: true,
                },
                created_at: {
                    allowNull: false,
                    type: Sequelize.DATE,
                },
                updated_at: {
                    allowNull: false,
                    type: Sequelize.DATE,
                },
                deleted_at: {
                    type: Sequelize.DATE,
                },
            },
            {
                underscored: true,
            },
        )
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.dropTable('statuses')
    },
}
