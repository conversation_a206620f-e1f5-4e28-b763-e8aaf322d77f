'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up(queryInterface, Sequelize) {
        await queryInterface.createTable(
            'user_project_mapping',
            {
                id: {
                    allowNull: false,
                    autoIncrement: true,
                    primaryKey: true,
                    type: Sequelize.INTEGER,
                },
                user_id: {
                    type: Sequelize.INTEGER,
                    allowNull: false,
                    references: {
                        model: 'users',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                project_id: {
                    type: Sequelize.INTEGER,
                    allowNull: false,
                    references: {
                        model: 'projects',
                        key: 'id',
                    },
                    onUpdate: 'CASCADE',
                    onDelete: 'CASCADE',
                },
                created_at: {
                    allowNull: false,
                    type: Sequelize.DATE,
                },
                updated_at: {
                    allowNull: false,
                    type: Sequelize.DATE,
                },
                deleted_at: {
                    type: Sequelize.DATE,
                    allowNull: true,
                },
            },
            {
                underscored: true,
                paranoid: true,
            },
        )

        // Add a unique constraint to prevent duplicate mappings
        await queryInterface.addIndex('user_project_mapping', ['user_id', 'project_id'], {
            unique: true,
            name: 'unique_user_project_mapping',
        })
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.dropTable('user_project_mapping')
    },
}
