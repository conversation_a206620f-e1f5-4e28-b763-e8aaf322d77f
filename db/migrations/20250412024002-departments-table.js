'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up(queryInterface, Sequelize) {
        await queryInterface.createTable(
            'departments',
            {
                id: {
                    type: Sequelize.INTEGER,
                    primaryKey: true,
                    autoIncrement: true,
                    allowNull: false,
                },
                short_code: {
                    type: Sequelize.STRING,
                    allowNull: false,
                },
                label: {
                    type: Sequelize.STRING,
                    allowNull: false,
                },
                colour: {
                    type: Sequelize.JSONB,
                    allowNull: true,
                },
                icon: {
                    type: Sequelize.STRING,
                    allowNull: true,
                },
                created_at: {
                    allowNull: false,
                    type: Sequelize.DATE,
                },
                updated_at: {
                    allowNull: false,
                    type: Sequelize.DATE,
                },
                deleted_at: {
                    type: Sequelize.DATE,
                },
            },
            {
                underscored: true,
                paranoid: true,
                timestamps: true,
            },
        )
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.dropTable('departments')
    },
}
