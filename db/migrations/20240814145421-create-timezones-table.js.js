'use strict'
/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up(queryInterface, Sequelize) {
        await queryInterface.createTable(
            'timezones',
            {
                id: {
                    allowNull: false,
                    autoIncrement: true,
                    primaryKey: true,
                    type: Sequelize.INTEGER,
                },
                timezone_name: {
                    type: Sequelize.STRING(50), // IANA timezone identifier (e.g., 'Africa/Abidjan')
                    allowNull: false,
                    unique: true,
                },
                display_name: {
                    type: Sequelize.STRING(200), // Display format (e.g., 'Abidjan, Ivory Coast')
                    allowNull: false,
                },
                city: {
                    type: Sequelize.STRING(100), // City name (e.g., 'Abidjan')
                    allowNull: false,
                },
                country_name: {
                    type: Sequelize.STRING(100), // Full country name (e.g., 'Ivory Coast')
                    allowNull: false,
                },
                country_code: {
                    type: Sequelize.STRING(2), // ISO 3166-1 alpha-2 country codes (e.g., 'CI')
                    allowNull: false,
                },
                gmt_offset: {
                    type: Sequelize.STRING(10), // Current GMT offset (e.g., '+0:00', '-9:00')
                    allowNull: false,
                },
                dst_offset: {
                    type: Sequelize.STRING(10), // DST offset when applicable (e.g., '+1:00', 'N/A')
                    allowNull: true,
                },
                is_dst_active: {
                    type: Sequelize.BOOLEAN, // Whether DST is currently active
                    defaultValue: false,
                },
                created_at: {
                    allowNull: false,
                    type: Sequelize.DATE,
                },
                updated_at: {
                    allowNull: false,
                    type: Sequelize.DATE,
                },
            },
            {
                underscored: true,
            }
        )

        // Add indexes for common queries
        await queryInterface.addIndex('timezones', ['timezone_name'], {
            unique: true,
            name: 'timezones_timezone_name_unique',
        })

        await queryInterface.addIndex('timezones', ['country_code'], {
            name: 'timezones_country_code_index',
        })

        await queryInterface.addIndex('timezones', ['gmt_offset'], {
            name: 'timezones_gmt_offset_index',
        })
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.removeIndex('timezones', 'timezones_timezone_name_unique')
        await queryInterface.removeIndex('timezones', 'timezones_country_code_index')
        await queryInterface.removeIndex('timezones', 'timezones_gmt_offset_index')
        await queryInterface.dropTable('timezones')
    },
}
