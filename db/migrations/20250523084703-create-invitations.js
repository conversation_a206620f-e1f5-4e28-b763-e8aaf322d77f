'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up(queryInterface, Sequelize) {
        await queryInterface.createTable('invitations', {
            id: {
                allowNull: false,
                autoIncrement: true,
                primaryKey: true,
                type: Sequelize.INTEGER,
            },
            workspace_id: {
                type: Sequelize.INTEGER,
                allowNull: false,
                references: {
                    model: 'workspaces',
                    key: 'id',
                },
                onUpdate: 'CASCADE',
                onDelete: 'CASCADE',
            },
            email: {
                type: Sequelize.STRING,
                allowNull: true,
            },
            invite_token: {
                type: Sequelize.STRING,
                allowNull: false,
                unique: true,
            },
            expires_at: {
                type: Sequelize.DATE,
                allowNull: false,
            },
            status: {
                type: Sequelize.ENUM('pending', 'accepted', 'expired'),
                allowNull: false,
                defaultValue: 'pending',
            },
            invited_by: {
                type: Sequelize.INTEGER,
                allowNull: false,
                references: {
                    model: 'users',
                    key: 'id',
                },
                onUpdate: 'CASCADE',
            },
            created_at: {
                allowNull: false,
                type: Sequelize.DATE,
                defaultValue: Sequelize.fn('NOW'),
            },
            updated_at: {
                allowNull: false,
                type: Sequelize.DATE,
                defaultValue: Sequelize.fn('NOW'),
            },
            deleted_at: {
                type: Sequelize.DATE,
                allowNull: true,
            },
        })
    },

    async down(queryInterface, Sequelize) {
        // Drop ENUM type separately if used
        await queryInterface.dropTable('invitations')
        await queryInterface.sequelize.query('DROP TYPE IF EXISTS enum_invitations_status;')
    },
}
