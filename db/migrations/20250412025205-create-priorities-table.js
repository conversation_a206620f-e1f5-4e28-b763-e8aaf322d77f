'use strict'

module.exports = {
    up: async (queryInterface, Sequelize) => {
        await queryInterface.createTable(
            'priorities',
            {
                id: {
                    allowNull: false,
                    autoIncrement: true,
                    primaryKey: true,
                    type: Sequelize.INTEGER,
                },
                label: {
                    type: Sequelize.STRING,
                    allowNull: false,
                    unique: true,
                },
                level: {
                    type: Sequelize.INTEGER,
                    allowNull: false,
                    defaultValue: 1,
                },
                color: {
                    type: Sequelize.STRING(7), // Hex color code
                    allowNull: true,
                },
                is_active: {
                    type: Sequelize.BOOLEAN,
                    defaultValue: true,
                    allowNull: false,
                },
                created_at: {
                    allowNull: false,
                    type: Sequelize.DATE,
                    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
                },
                updated_at: {
                    allowNull: false,
                    type: Sequelize.DATE,
                    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
                },
            },
            {
                underscored: true,
                tableName: 'priorities',
                timestamps: true,
                createdAt: 'created_at',
                updatedAt: 'updated_at',
            }
        )
    },

    down: async (queryInterface, Sequelize) => {
        await queryInterface.dropTable('priorities')
    },
}
