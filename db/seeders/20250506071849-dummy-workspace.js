'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up(queryInterface, Sequelize) {
        await queryInterface.bulkInsert('workspaces', [
            {
                name: 'Development Workspace',
                invite_link: '',
                img_url: '',
                plan: 1,
                created_at: new Date(),
                updated_at: new Date(),
            },
        ])
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.bulkDelete('workspaces', null, {})
    },
}
