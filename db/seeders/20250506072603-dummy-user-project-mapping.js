'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up(queryInterface, Sequelize) {
        const now = new Date()

        await queryInterface.bulkInsert('user_project_mapping', [
            {
                user_id: 1, // Assuming User ID 1 exists
                project_id: 1, // Assuming Project ID 1 exists
                created_at: now,
                updated_at: now,
            },
            {
                user_id: 2, // Assuming User ID 2 exists
                project_id: 1, // Assuming Project ID 1 exists
                created_at: now,
                updated_at: now,
            },
        ])
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.bulkDelete('user_project_mapping', null, {})
    },
}
