'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up(queryInterface, Sequelize) {
        await queryInterface.bulkInsert(
            'timezones',
            [
                {
                    timezone_name: 'Africa/Abidjan',
                    display_name: 'Abidjan, Ivory Coast',
                    city: 'Abidjan',
                    country_name: 'Ivory Coast',
                    country_code: 'CI',
                    gmt_offset: '+0:00',
                    dst_offset: null,
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    timezone_name: 'America/Adak',
                    display_name: 'Adak, Hawaii, United States',
                    city: 'Adak',
                    country_name: 'United States',
                    country_code: 'US',
                    gmt_offset: '-10:00',
                    dst_offset: '-9:00',
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    timezone_name: 'Atlantic/Stanley',
                    display_name: 'Adamstown, Pitcairn',
                    city: 'Adamstown',
                    country_name: 'Pitcairn',
                    country_code: 'PN',
                    gmt_offset: '-8:00',
                    dst_offset: null,
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    timezone_name: 'Australia/Adelaide',
                    display_name: 'Adelaide, Australia',
                    city: 'Adelaide',
                    country_name: 'Australia',
                    country_code: 'AU',
                    gmt_offset: '+10:30',
                    dst_offset: '+11:30',
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    timezone_name: 'Asia/Damascus',
                    display_name: 'Aleppo, Syria',
                    city: 'Aleppo',
                    country_name: 'Syria',
                    country_code: 'SY',
                    gmt_offset: '+2:00',
                    dst_offset: '+3:00',
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    timezone_name: 'Africa/Algiers',
                    display_name: 'Algiers, Algeria',
                    city: 'Algiers',
                    country_name: 'Algeria',
                    country_code: 'DZ',
                    gmt_offset: '+1:00',
                    dst_offset: null,
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    timezone_name: 'Asia/Almaty',
                    display_name: 'Almaty, Kazakhstan',
                    city: 'Almaty',
                    country_name: 'Kazakhstan',
                    country_code: 'KZ',
                    gmt_offset: '+6:00',
                    dst_offset: null,
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },

                // AMERICA TIMEZONES
                {
                    timezone_name: 'America/New_York',
                    display_name: 'New York, United States',
                    city: 'New York',
                    country_name: 'United States',
                    country_code: 'US',
                    gmt_offset: '-5:00',
                    dst_offset: '-4:00',
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    timezone_name: 'America/Los_Angeles',
                    display_name: 'Los Angeles, United States',
                    city: 'Los Angeles',
                    country_name: 'United States',
                    country_code: 'US',
                    gmt_offset: '-8:00',
                    dst_offset: '-7:00',
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    timezone_name: 'America/Chicago',
                    display_name: 'Chicago, United States',
                    city: 'Chicago',
                    country_name: 'United States',
                    country_code: 'US',
                    gmt_offset: '-6:00',
                    dst_offset: '-5:00',
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    timezone_name: 'America/Denver',
                    display_name: 'Denver, United States',
                    city: 'Denver',
                    country_name: 'United States',
                    country_code: 'US',
                    gmt_offset: '-7:00',
                    dst_offset: '-6:00',
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    timezone_name: 'America/Phoenix',
                    display_name: 'Phoenix, United States',
                    city: 'Phoenix',
                    country_name: 'United States',
                    country_code: 'US',
                    gmt_offset: '-7:00',
                    dst_offset: null,
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    timezone_name: 'America/Toronto',
                    display_name: 'Toronto, Canada',
                    city: 'Toronto',
                    country_name: 'Canada',
                    country_code: 'CA',
                    gmt_offset: '-5:00',
                    dst_offset: '-4:00',
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    timezone_name: 'America/Vancouver',
                    display_name: 'Vancouver, Canada',
                    city: 'Vancouver',
                    country_name: 'Canada',
                    country_code: 'CA',
                    gmt_offset: '-8:00',
                    dst_offset: '-7:00',
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    timezone_name: 'America/Sao_Paulo',
                    display_name: 'São Paulo, Brazil',
                    city: 'São Paulo',
                    country_name: 'Brazil',
                    country_code: 'BR',
                    gmt_offset: '-3:00',
                    dst_offset: '-2:00',
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    timezone_name: 'America/Mexico_City',
                    display_name: 'Mexico City, Mexico',
                    city: 'Mexico City',
                    country_name: 'Mexico',
                    country_code: 'MX',
                    gmt_offset: '-6:00',
                    dst_offset: '-5:00',
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    timezone_name: 'America/Lima',
                    display_name: 'Lima, Peru',
                    city: 'Lima',
                    country_name: 'Peru',
                    country_code: 'PE',
                    gmt_offset: '-5:00',
                    dst_offset: null,
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    timezone_name: 'America/Buenos_Aires',
                    display_name: 'Buenos Aires, Argentina',
                    city: 'Buenos Aires',
                    country_name: 'Argentina',
                    country_code: 'AR',
                    gmt_offset: '-3:00',
                    dst_offset: null,
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },

                // UAE TIMEZONES
                {
                    timezone_name: 'Asia/Dubai',
                    display_name: 'Dubai, United Arab Emirates',
                    city: 'Dubai',
                    country_name: 'United Arab Emirates',
                    country_code: 'AE',
                    gmt_offset: '+4:00',
                    dst_offset: null,
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },

                // SAUDI ARABIA TIMEZONES
                {
                    timezone_name: 'Asia/Riyadh',
                    display_name: 'Riyadh, Saudi Arabia',
                    city: 'Riyadh',
                    country_name: 'Saudi Arabia',
                    country_code: 'SA',
                    gmt_offset: '+3:00',
                    dst_offset: null,
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },

                // QATAR TIMEZONES
                {
                    timezone_name: 'Asia/Qatar',
                    display_name: 'Doha, Qatar',
                    city: 'Doha',
                    country_name: 'Qatar',
                    country_code: 'QA',
                    gmt_offset: '+3:00',
                    dst_offset: null,
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },

                // INDIA TIMEZONES
                {
                    timezone_name: 'Asia/Kolkata',
                    display_name: 'Mumbai, India',
                    city: 'Mumbai',
                    country_name: 'India',
                    country_code: 'IN',
                    gmt_offset: '+5:30',
                    dst_offset: null,
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },

                // ADDITIONAL POPULAR TIMEZONES
                {
                    timezone_name: 'Europe/London',
                    display_name: 'London, United Kingdom',
                    city: 'London',
                    country_name: 'United Kingdom',
                    country_code: 'GB',
                    gmt_offset: '+0:00',
                    dst_offset: '+1:00',
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    timezone_name: 'Asia/Tokyo',
                    display_name: 'Tokyo, Japan',
                    city: 'Tokyo',
                    country_name: 'Japan',
                    country_code: 'JP',
                    gmt_offset: '+9:00',
                    dst_offset: null,
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },
                {
                    timezone_name: 'Australia/Sydney',
                    display_name: 'Sydney, Australia',
                    city: 'Sydney',
                    country_name: 'Australia',
                    country_code: 'AU',
                    gmt_offset: '+11:00',
                    dst_offset: '+10:00',
                    is_dst_active: false,
                    created_at: new Date(),
                    updated_at: new Date(),
                },
            ],
            {}
        )
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.bulkDelete('timezones', null, {})
    },
}
