'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const now = new Date()

    await queryInterface.bulkInsert('task_dependency_mapping', [
      {
        task_id: 1, // Implement user login
        dependant_task_id: 2, // Create login form UI (depends on login implementation)
        dependency_level: 1, // High dependency
        created_at: now,
        updated_at: now,
      },
      {
        task_id: 2, // Create login form UI
        dependant_task_id: 3, // Add login error handling (depends on login form)
        dependency_level: 1, // High dependency
        created_at: now,
        updated_at: now,
      },
      {
        task_id: 4, // Design user profile page
        dependant_task_id: 5, // Integrate profile form with backend
        dependency_level: 1, // High dependency
        created_at: now,
        updated_at: now,
      },
      {
        task_id: 5, // Integrate profile form with backend
        dependant_task_id: 6, // Add profile picture upload
        dependency_level: 2, // Medium dependency
        created_at: now,
        updated_at: now,
      },
    ])
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('task_dependency_mapping', null, {})
  },
}