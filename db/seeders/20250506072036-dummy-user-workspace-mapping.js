'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up(queryInterface, Sequelize) {
        await queryInterface.bulkInsert('user_workspace_mapping', [
            {
                user_id: 1,
                workspace_id: 1,
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                user_id: 2,
                workspace_id: 1,
                created_at: new Date(),
                updated_at: new Date(),
            },
        ])
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.bulkDelete('user_workspace_mapping', null, {})
    },
}
