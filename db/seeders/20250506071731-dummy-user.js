'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
    async up(queryInterface, Sequelize) {
        const { passwordUtils } = await import('../../src/utils/password.util.js')

        const hashedPassword = await passwordUtils.hashPassword('Password@123')

        await queryInterface.bulkInsert('users', [
            {
                user_type: 1,
                first_name: '<PERSON>',
                last_name: '<PERSON>',
                email: '<EMAIL>',
                password: hashedPassword,
                img_url: null,
                verification_code: null,
                verification_code_expires_at: null,
                is_verified: true,
                refresh_token: null,
                created_at: new Date(),
                updated_at: new Date(),
            },
            {
                user_type: 2,
                first_name: '<PERSON>',
                last_name: '<PERSON>',
                email: '<EMAIL>',
                password: hashedPassword,
                img_url: null,
                verification_code: null,
                verification_code_expires_at: null,
                is_verified: true,
                refresh_token: null,
                created_at: new Date(),
                updated_at: new Date(),
            },
        ])
    },

    async down(queryInterface, Sequelize) {
        await queryInterface.bulkDelete('users', null, {})
    },
}
